# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .utterance_response_model import UtteranceResponseModel


class SpeakerResponseModel(UncheckedBaseModel):
    speaker_id: str = pydantic.Field()
    """
    The ID of the speaker.
    """

    duration_secs: float = pydantic.Field()
    """
    The duration of the speaker segment in seconds.
    """

    utterances: typing.Optional[typing.List[UtteranceResponseModel]] = pydantic.Field(default=None)
    """
    The utterances of the speaker.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
