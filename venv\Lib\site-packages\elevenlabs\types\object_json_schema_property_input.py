# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2, update_forward_refs
from ..core.unchecked_base_model import UncheckedBaseModel


class ObjectJsonSchemaPropertyInput(UncheckedBaseModel):
    type: typing.Optional[typing.Literal["object"]] = None
    required: typing.Optional[typing.List[str]] = None
    description: typing.Optional[str] = None
    properties: typing.Optional[typing.Dict[str, "ObjectJsonSchemaPropertyInputPropertiesValue"]] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


from .object_json_schema_property_input_properties_value import ObjectJsonSchemaPropertyInputPropertiesValue  # noqa: E402, F401, I001

update_forward_refs(ObjectJsonSchemaPropertyInput)
