# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel


class VoiceSampleVisualWaveformResponseModel(UncheckedBaseModel):
    sample_id: str = pydantic.Field()
    """
    The ID of the sample.
    """

    visual_waveform: typing.List[float] = pydantic.Field()
    """
    The visual waveform of the sample, represented as a list of floats.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
