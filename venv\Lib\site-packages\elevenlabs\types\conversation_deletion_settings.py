# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel


class ConversationDeletionSettings(UncheckedBaseModel):
    deletion_time_unix_secs: typing.Optional[int] = None
    deleted_logs_at_time_unix_secs: typing.Optional[int] = None
    deleted_audio_at_time_unix_secs: typing.Optional[int] = None
    deleted_transcript_at_time_unix_secs: typing.Optional[int] = None
    delete_transcript_and_pii: typing.Optional[bool] = None
    delete_audio: typing.Optional[bool] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
