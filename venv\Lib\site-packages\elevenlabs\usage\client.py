# This file was auto-generated by <PERSON>rn from our API Definition.

import typing

from ..core.client_wrapper import Async<PERSON><PERSON><PERSON>rapper, SyncClientWrapper
from ..core.request_options import RequestOptions
from ..types.breakdown_types import BreakdownTypes
from ..types.metric_type import MetricType
from ..types.usage_aggregation_interval import UsageAggregationInterval
from ..types.usage_characters_response_model import UsageCharactersResponseModel
from .raw_client import AsyncRawUsageClient, RawUsageClient


class UsageClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawUsageClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawUsageClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawUsageClient
        """
        return self._raw_client

    def get(
        self,
        *,
        start_unix: int,
        end_unix: int,
        include_workspace_metrics: typing.Optional[bool] = None,
        breakdown_type: typing.Optional[BreakdownTypes] = None,
        aggregation_interval: typing.Optional[UsageAggregationInterval] = None,
        metric: typing.Optional[MetricType] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> UsageCharactersResponseModel:
        """
        Returns the usage metrics for the current user or the entire workspace they are part of. The response provides a time axis based on the specified aggregation interval (default: day), with usage values for each interval along that axis. Usage is broken down by the selected breakdown type. For example, breakdown type "voice" will return the usage of each voice for each interval along the time axis.

        Parameters
        ----------
        start_unix : int
            UTC Unix timestamp for the start of the usage window, in milliseconds. To include the first day of the window, the timestamp should be at 00:00:00 of that day.

        end_unix : int
            UTC Unix timestamp for the end of the usage window, in milliseconds. To include the last day of the window, the timestamp should be at 23:59:59 of that day.

        include_workspace_metrics : typing.Optional[bool]
            Whether or not to include the statistics of the entire workspace.

        breakdown_type : typing.Optional[BreakdownTypes]
            How to break down the information. Cannot be "user" if include_workspace_metrics is False.

        aggregation_interval : typing.Optional[UsageAggregationInterval]
            How to aggregate usage data over time. Can be "hour", "day", "week", "month", or "cumulative".

        metric : typing.Optional[MetricType]
            Which metric to aggregate.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        UsageCharactersResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.usage.get(
            start_unix=1,
            end_unix=1,
        )
        """
        _response = self._raw_client.get(
            start_unix=start_unix,
            end_unix=end_unix,
            include_workspace_metrics=include_workspace_metrics,
            breakdown_type=breakdown_type,
            aggregation_interval=aggregation_interval,
            metric=metric,
            request_options=request_options,
        )
        return _response.data


class AsyncUsageClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawUsageClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawUsageClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawUsageClient
        """
        return self._raw_client

    async def get(
        self,
        *,
        start_unix: int,
        end_unix: int,
        include_workspace_metrics: typing.Optional[bool] = None,
        breakdown_type: typing.Optional[BreakdownTypes] = None,
        aggregation_interval: typing.Optional[UsageAggregationInterval] = None,
        metric: typing.Optional[MetricType] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> UsageCharactersResponseModel:
        """
        Returns the usage metrics for the current user or the entire workspace they are part of. The response provides a time axis based on the specified aggregation interval (default: day), with usage values for each interval along that axis. Usage is broken down by the selected breakdown type. For example, breakdown type "voice" will return the usage of each voice for each interval along the time axis.

        Parameters
        ----------
        start_unix : int
            UTC Unix timestamp for the start of the usage window, in milliseconds. To include the first day of the window, the timestamp should be at 00:00:00 of that day.

        end_unix : int
            UTC Unix timestamp for the end of the usage window, in milliseconds. To include the last day of the window, the timestamp should be at 23:59:59 of that day.

        include_workspace_metrics : typing.Optional[bool]
            Whether or not to include the statistics of the entire workspace.

        breakdown_type : typing.Optional[BreakdownTypes]
            How to break down the information. Cannot be "user" if include_workspace_metrics is False.

        aggregation_interval : typing.Optional[UsageAggregationInterval]
            How to aggregate usage data over time. Can be "hour", "day", "week", "month", or "cumulative".

        metric : typing.Optional[MetricType]
            Which metric to aggregate.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        UsageCharactersResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.usage.get(
                start_unix=1,
                end_unix=1,
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.get(
            start_unix=start_unix,
            end_unix=end_unix,
            include_workspace_metrics=include_workspace_metrics,
            breakdown_type=breakdown_type,
            aggregation_interval=aggregation_interval,
            metric=metric,
            request_options=request_options,
        )
        return _response.data
