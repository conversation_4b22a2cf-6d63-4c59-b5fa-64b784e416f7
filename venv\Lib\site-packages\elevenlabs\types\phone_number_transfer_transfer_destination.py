# This file was auto-generated by <PERSON>rn from our API Definition.

from __future__ import annotations

import typing

import pydantic
import typing_extensions
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel, UnionMetadata


class PhoneNumberTransferTransferDestination_Phone(UncheckedBaseModel):
    type: typing.Literal["phone"] = "phone"
    phone_number: str

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class PhoneNumberTransferTransferDestination_SipUri(UncheckedBaseModel):
    type: typing.Literal["sip_uri"] = "sip_uri"
    sip_uri: str

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


PhoneNumberTransferTransferDestination = typing_extensions.Annotated[
    typing.Union[PhoneNumberTransferTransferDestination_Phone, PhoneNumberTransferTransferDestination_SipUri],
    UnionMetadata(discriminant="type"),
]
