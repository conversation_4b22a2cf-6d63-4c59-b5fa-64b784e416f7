# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .moderation_status_response_model import ModerationStatusResponseModel
from .subscription_usage_response_model import SubscriptionUsageResponseModel


class SubscriptionExtrasResponseModel(UncheckedBaseModel):
    concurrency: int = pydantic.Field()
    """
    The concurrency of the user.
    """

    convai_concurrency: int = pydantic.Field()
    """
    The Convai concurrency of the user.
    """

    convai_chars_per_minute: typing.Optional[int] = pydantic.Field(default=None)
    """
    The Convai characters per minute of the user.
    """

    convai_asr_chars_per_minute: typing.Optional[int] = pydantic.Field(default=None)
    """
    The Convai ASR characters per minute of the user.
    """

    force_logging_disabled: bool = pydantic.Field()
    """
    Whether the user's logging is disabled.
    """

    can_request_manual_pro_voice_verification: bool = pydantic.Field()
    """
    Whether the user can request manual pro voice verification.
    """

    can_bypass_voice_captcha: bool = pydantic.Field()
    """
    Whether the user can bypass the voice captcha.
    """

    moderation: ModerationStatusResponseModel = pydantic.Field()
    """
    The moderation status of the user.
    """

    unused_characters_rolled_over_from_previous_period: typing.Optional[int] = pydantic.Field(default=None)
    """
    The unused characters rolled over from the previous period.
    """

    overused_characters_rolled_over_from_previous_period: typing.Optional[int] = pydantic.Field(default=None)
    """
    The overused characters rolled over from the previous period.
    """

    usage: typing.Optional[SubscriptionUsageResponseModel] = pydantic.Field(default=None)
    """
    Data on how the subscription is being used.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
