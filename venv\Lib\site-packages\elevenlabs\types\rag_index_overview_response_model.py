# This file was auto-generated by <PERSON>rn from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .rag_index_overview_embedding_model_response_model import RagIndexOverviewEmbeddingModelResponseModel


class RagIndexOverviewResponseModel(UncheckedBaseModel):
    total_used_bytes: int
    total_max_bytes: int
    models: typing.List[RagIndexOverviewEmbeddingModelResponseModel]

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
