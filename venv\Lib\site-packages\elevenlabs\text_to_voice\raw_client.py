# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing
from json.decoder import <PERSON><PERSON><PERSON>ecodeError

from ..core.api_error import ApiError
from ..core.client_wrapper import Async<PERSON>lientWrapper, SyncClientWrapper
from ..core.http_response import Async<PERSON>ttpR<PERSON>ponse, HttpResponse
from ..core.request_options import RequestOptions
from ..core.unchecked_base_model import construct_type
from ..errors.unprocessable_entity_error import UnprocessableEntityError
from ..types.http_validation_error import HttpValidationError
from ..types.voice import Voice
from ..types.voice_design_preview_response import VoiceDesignPreviewResponse
from .types.text_to_voice_create_previews_request_output_format import TextToVoiceCreatePreviewsRequestOutputFormat
from .types.text_to_voice_design_request_output_format import TextToVoiceDesignRequestOutputFormat
from .types.voice_design_request_model_model_id import VoiceDesignRequestModelModelId

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class RawTextToVoiceClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    def create_previews(
        self,
        *,
        voice_description: str,
        output_format: typing.Optional[TextToVoiceCreatePreviewsRequestOutputFormat] = None,
        text: typing.Optional[str] = OMIT,
        auto_generate_text: typing.Optional[bool] = OMIT,
        loudness: typing.Optional[float] = OMIT,
        quality: typing.Optional[float] = OMIT,
        seed: typing.Optional[int] = OMIT,
        guidance_scale: typing.Optional[float] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> HttpResponse[VoiceDesignPreviewResponse]:
        """
        Create a voice from a text prompt.

        Parameters
        ----------
        voice_description : str
            Description to use for the created voice.

        output_format : typing.Optional[TextToVoiceCreatePreviewsRequestOutputFormat]
            The output format of the generated audio.

        text : typing.Optional[str]
            Text to generate, text length has to be between 100 and 1000.

        auto_generate_text : typing.Optional[bool]
            Whether to automatically generate a text suitable for the voice description.

        loudness : typing.Optional[float]
            Controls the volume level of the generated voice. -1 is quietest, 1 is loudest, 0 corresponds to roughly -24 LUFS.

        quality : typing.Optional[float]
            Higher quality results in better voice output but less variety.

        seed : typing.Optional[int]
            Random number that controls the voice generation. Same seed with same inputs produces same voice.

        guidance_scale : typing.Optional[float]
            Controls how closely the AI follows the prompt. Lower numbers give the AI more freedom to be creative, while higher numbers force it to stick more to the prompt. High numbers can cause voice to sound artificial or robotic. We recommend to use longer, more detailed prompts at lower Guidance Scale.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[VoiceDesignPreviewResponse]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/text-to-voice/create-previews",
            method="POST",
            params={
                "output_format": output_format,
            },
            json={
                "voice_description": voice_description,
                "text": text,
                "auto_generate_text": auto_generate_text,
                "loudness": loudness,
                "quality": quality,
                "seed": seed,
                "guidance_scale": guidance_scale,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    VoiceDesignPreviewResponse,
                    construct_type(
                        type_=VoiceDesignPreviewResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    def create_voice_from_preview(
        self,
        *,
        voice_name: str,
        voice_description: str,
        generated_voice_id: str,
        labels: typing.Optional[typing.Dict[str, typing.Optional[str]]] = OMIT,
        played_not_selected_voice_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> HttpResponse[Voice]:
        """
        Add a generated voice to the voice library.

        Parameters
        ----------
        voice_name : str
            Name to use for the created voice.

        voice_description : str
            Description to use for the created voice.

        generated_voice_id : str
            The generated_voice_id to create, call POST /v1/text-to-voice/create-previews and fetch the generated_voice_id from the response header if don't have one yet.

        labels : typing.Optional[typing.Dict[str, typing.Optional[str]]]
            Optional, metadata to add to the created voice. Defaults to None.

        played_not_selected_voice_ids : typing.Optional[typing.Sequence[str]]
            List of voice ids that the user has played but not selected. Used for RLHF.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[Voice]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/text-to-voice/create-voice-from-preview",
            method="POST",
            json={
                "voice_name": voice_name,
                "voice_description": voice_description,
                "generated_voice_id": generated_voice_id,
                "labels": labels,
                "played_not_selected_voice_ids": played_not_selected_voice_ids,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    Voice,
                    construct_type(
                        type_=Voice,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    def create(
        self,
        *,
        voice_name: str,
        voice_description: str,
        generated_voice_id: str,
        labels: typing.Optional[typing.Dict[str, typing.Optional[str]]] = OMIT,
        played_not_selected_voice_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> HttpResponse[Voice]:
        """
        Create a voice from previously generated voice preview. This endpoint should be called after you fetched a generated_voice_id using POST /v1/text-to-voice/design or POST /v1/text-to-voice/:voice_id/remix.

        Parameters
        ----------
        voice_name : str
            Name to use for the created voice.

        voice_description : str
            Description to use for the created voice.

        generated_voice_id : str
            The generated_voice_id to create, call POST /v1/text-to-voice/create-previews and fetch the generated_voice_id from the response header if don't have one yet.

        labels : typing.Optional[typing.Dict[str, typing.Optional[str]]]
            Optional, metadata to add to the created voice. Defaults to None.

        played_not_selected_voice_ids : typing.Optional[typing.Sequence[str]]
            List of voice ids that the user has played but not selected. Used for RLHF.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[Voice]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/text-to-voice",
            method="POST",
            json={
                "voice_name": voice_name,
                "voice_description": voice_description,
                "generated_voice_id": generated_voice_id,
                "labels": labels,
                "played_not_selected_voice_ids": played_not_selected_voice_ids,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    Voice,
                    construct_type(
                        type_=Voice,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    def design(
        self,
        *,
        voice_description: str,
        output_format: typing.Optional[TextToVoiceDesignRequestOutputFormat] = None,
        model_id: typing.Optional[VoiceDesignRequestModelModelId] = OMIT,
        text: typing.Optional[str] = OMIT,
        auto_generate_text: typing.Optional[bool] = OMIT,
        loudness: typing.Optional[float] = OMIT,
        seed: typing.Optional[int] = OMIT,
        guidance_scale: typing.Optional[float] = OMIT,
        stream_previews: typing.Optional[bool] = OMIT,
        quality: typing.Optional[float] = OMIT,
        reference_audio_base_64: typing.Optional[str] = OMIT,
        prompt_strength: typing.Optional[float] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> HttpResponse[VoiceDesignPreviewResponse]:
        """
        Design a voice via a prompt. This method returns a list of voice previews. Each preview has a generated_voice_id and a sample of the voice as base64 encoded mp3 audio. To create a voice use the generated_voice_id of the preferred preview with the /v1/text-to-voice endpoint.

        Parameters
        ----------
        voice_description : str
            Description to use for the created voice.

        output_format : typing.Optional[TextToVoiceDesignRequestOutputFormat]
            Output format of the generated audio. Formatted as codec_sample_rate_bitrate. So an mp3 with 22.05kHz sample rate at 32kbs is represented as mp3_22050_32. MP3 with 192kbps bitrate requires you to be subscribed to Creator tier or above. PCM with 44.1kHz sample rate requires you to be subscribed to Pro tier or above. Note that the μ-law format (sometimes written mu-law, often approximated as u-law) is commonly used for Twilio audio inputs.

        model_id : typing.Optional[VoiceDesignRequestModelModelId]
            Model to use for the voice generation. Possible values: eleven_multilingual_ttv_v2, eleven_ttv_v3.

        text : typing.Optional[str]
            Text to generate, text length has to be between 100 and 1000.

        auto_generate_text : typing.Optional[bool]
            Whether to automatically generate a text suitable for the voice description.

        loudness : typing.Optional[float]
            Controls the volume level of the generated voice. -1 is quietest, 1 is loudest, 0 corresponds to roughly -24 LUFS.

        seed : typing.Optional[int]
            Random number that controls the voice generation. Same seed with same inputs produces same voice.

        guidance_scale : typing.Optional[float]
            Controls how closely the AI follows the prompt. Lower numbers give the AI more freedom to be creative, while higher numbers force it to stick more to the prompt. High numbers can cause voice to sound artificial or robotic. We recommend to use longer, more detailed prompts at lower Guidance Scale.

        stream_previews : typing.Optional[bool]
            Determines whether the Text to Voice previews should be included in the response. If true, only the generated IDs will be returned which can then be streamed via the /v1/text-to-voice/:generated_voice_id/stream endpoint.

        quality : typing.Optional[float]
            Higher quality results in better voice output but less variety.

        reference_audio_base_64 : typing.Optional[str]
            Reference audio to use for the voice generation. The audio should be base64 encoded. Only supported when using the  eleven_ttv_v3 model.

        prompt_strength : typing.Optional[float]
            Controls the balance of prompt versus reference audio when generating voice samples. 0 means almost no prompt influence, 1 means almost no reference audio influence. Only supported when using the eleven_ttv_v3 model and providing reference audio.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[VoiceDesignPreviewResponse]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/text-to-voice/design",
            method="POST",
            params={
                "output_format": output_format,
            },
            json={
                "voice_description": voice_description,
                "model_id": model_id,
                "text": text,
                "auto_generate_text": auto_generate_text,
                "loudness": loudness,
                "seed": seed,
                "guidance_scale": guidance_scale,
                "stream_previews": stream_previews,
                "quality": quality,
                "reference_audio_base64": reference_audio_base_64,
                "prompt_strength": prompt_strength,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    VoiceDesignPreviewResponse,
                    construct_type(
                        type_=VoiceDesignPreviewResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)


class AsyncRawTextToVoiceClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def create_previews(
        self,
        *,
        voice_description: str,
        output_format: typing.Optional[TextToVoiceCreatePreviewsRequestOutputFormat] = None,
        text: typing.Optional[str] = OMIT,
        auto_generate_text: typing.Optional[bool] = OMIT,
        loudness: typing.Optional[float] = OMIT,
        quality: typing.Optional[float] = OMIT,
        seed: typing.Optional[int] = OMIT,
        guidance_scale: typing.Optional[float] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AsyncHttpResponse[VoiceDesignPreviewResponse]:
        """
        Create a voice from a text prompt.

        Parameters
        ----------
        voice_description : str
            Description to use for the created voice.

        output_format : typing.Optional[TextToVoiceCreatePreviewsRequestOutputFormat]
            The output format of the generated audio.

        text : typing.Optional[str]
            Text to generate, text length has to be between 100 and 1000.

        auto_generate_text : typing.Optional[bool]
            Whether to automatically generate a text suitable for the voice description.

        loudness : typing.Optional[float]
            Controls the volume level of the generated voice. -1 is quietest, 1 is loudest, 0 corresponds to roughly -24 LUFS.

        quality : typing.Optional[float]
            Higher quality results in better voice output but less variety.

        seed : typing.Optional[int]
            Random number that controls the voice generation. Same seed with same inputs produces same voice.

        guidance_scale : typing.Optional[float]
            Controls how closely the AI follows the prompt. Lower numbers give the AI more freedom to be creative, while higher numbers force it to stick more to the prompt. High numbers can cause voice to sound artificial or robotic. We recommend to use longer, more detailed prompts at lower Guidance Scale.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[VoiceDesignPreviewResponse]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/text-to-voice/create-previews",
            method="POST",
            params={
                "output_format": output_format,
            },
            json={
                "voice_description": voice_description,
                "text": text,
                "auto_generate_text": auto_generate_text,
                "loudness": loudness,
                "quality": quality,
                "seed": seed,
                "guidance_scale": guidance_scale,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    VoiceDesignPreviewResponse,
                    construct_type(
                        type_=VoiceDesignPreviewResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    async def create_voice_from_preview(
        self,
        *,
        voice_name: str,
        voice_description: str,
        generated_voice_id: str,
        labels: typing.Optional[typing.Dict[str, typing.Optional[str]]] = OMIT,
        played_not_selected_voice_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AsyncHttpResponse[Voice]:
        """
        Add a generated voice to the voice library.

        Parameters
        ----------
        voice_name : str
            Name to use for the created voice.

        voice_description : str
            Description to use for the created voice.

        generated_voice_id : str
            The generated_voice_id to create, call POST /v1/text-to-voice/create-previews and fetch the generated_voice_id from the response header if don't have one yet.

        labels : typing.Optional[typing.Dict[str, typing.Optional[str]]]
            Optional, metadata to add to the created voice. Defaults to None.

        played_not_selected_voice_ids : typing.Optional[typing.Sequence[str]]
            List of voice ids that the user has played but not selected. Used for RLHF.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[Voice]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/text-to-voice/create-voice-from-preview",
            method="POST",
            json={
                "voice_name": voice_name,
                "voice_description": voice_description,
                "generated_voice_id": generated_voice_id,
                "labels": labels,
                "played_not_selected_voice_ids": played_not_selected_voice_ids,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    Voice,
                    construct_type(
                        type_=Voice,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    async def create(
        self,
        *,
        voice_name: str,
        voice_description: str,
        generated_voice_id: str,
        labels: typing.Optional[typing.Dict[str, typing.Optional[str]]] = OMIT,
        played_not_selected_voice_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AsyncHttpResponse[Voice]:
        """
        Create a voice from previously generated voice preview. This endpoint should be called after you fetched a generated_voice_id using POST /v1/text-to-voice/design or POST /v1/text-to-voice/:voice_id/remix.

        Parameters
        ----------
        voice_name : str
            Name to use for the created voice.

        voice_description : str
            Description to use for the created voice.

        generated_voice_id : str
            The generated_voice_id to create, call POST /v1/text-to-voice/create-previews and fetch the generated_voice_id from the response header if don't have one yet.

        labels : typing.Optional[typing.Dict[str, typing.Optional[str]]]
            Optional, metadata to add to the created voice. Defaults to None.

        played_not_selected_voice_ids : typing.Optional[typing.Sequence[str]]
            List of voice ids that the user has played but not selected. Used for RLHF.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[Voice]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/text-to-voice",
            method="POST",
            json={
                "voice_name": voice_name,
                "voice_description": voice_description,
                "generated_voice_id": generated_voice_id,
                "labels": labels,
                "played_not_selected_voice_ids": played_not_selected_voice_ids,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    Voice,
                    construct_type(
                        type_=Voice,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)

    async def design(
        self,
        *,
        voice_description: str,
        output_format: typing.Optional[TextToVoiceDesignRequestOutputFormat] = None,
        model_id: typing.Optional[VoiceDesignRequestModelModelId] = OMIT,
        text: typing.Optional[str] = OMIT,
        auto_generate_text: typing.Optional[bool] = OMIT,
        loudness: typing.Optional[float] = OMIT,
        seed: typing.Optional[int] = OMIT,
        guidance_scale: typing.Optional[float] = OMIT,
        stream_previews: typing.Optional[bool] = OMIT,
        quality: typing.Optional[float] = OMIT,
        reference_audio_base_64: typing.Optional[str] = OMIT,
        prompt_strength: typing.Optional[float] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AsyncHttpResponse[VoiceDesignPreviewResponse]:
        """
        Design a voice via a prompt. This method returns a list of voice previews. Each preview has a generated_voice_id and a sample of the voice as base64 encoded mp3 audio. To create a voice use the generated_voice_id of the preferred preview with the /v1/text-to-voice endpoint.

        Parameters
        ----------
        voice_description : str
            Description to use for the created voice.

        output_format : typing.Optional[TextToVoiceDesignRequestOutputFormat]
            Output format of the generated audio. Formatted as codec_sample_rate_bitrate. So an mp3 with 22.05kHz sample rate at 32kbs is represented as mp3_22050_32. MP3 with 192kbps bitrate requires you to be subscribed to Creator tier or above. PCM with 44.1kHz sample rate requires you to be subscribed to Pro tier or above. Note that the μ-law format (sometimes written mu-law, often approximated as u-law) is commonly used for Twilio audio inputs.

        model_id : typing.Optional[VoiceDesignRequestModelModelId]
            Model to use for the voice generation. Possible values: eleven_multilingual_ttv_v2, eleven_ttv_v3.

        text : typing.Optional[str]
            Text to generate, text length has to be between 100 and 1000.

        auto_generate_text : typing.Optional[bool]
            Whether to automatically generate a text suitable for the voice description.

        loudness : typing.Optional[float]
            Controls the volume level of the generated voice. -1 is quietest, 1 is loudest, 0 corresponds to roughly -24 LUFS.

        seed : typing.Optional[int]
            Random number that controls the voice generation. Same seed with same inputs produces same voice.

        guidance_scale : typing.Optional[float]
            Controls how closely the AI follows the prompt. Lower numbers give the AI more freedom to be creative, while higher numbers force it to stick more to the prompt. High numbers can cause voice to sound artificial or robotic. We recommend to use longer, more detailed prompts at lower Guidance Scale.

        stream_previews : typing.Optional[bool]
            Determines whether the Text to Voice previews should be included in the response. If true, only the generated IDs will be returned which can then be streamed via the /v1/text-to-voice/:generated_voice_id/stream endpoint.

        quality : typing.Optional[float]
            Higher quality results in better voice output but less variety.

        reference_audio_base_64 : typing.Optional[str]
            Reference audio to use for the voice generation. The audio should be base64 encoded. Only supported when using the  eleven_ttv_v3 model.

        prompt_strength : typing.Optional[float]
            Controls the balance of prompt versus reference audio when generating voice samples. 0 means almost no prompt influence, 1 means almost no reference audio influence. Only supported when using the eleven_ttv_v3 model and providing reference audio.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[VoiceDesignPreviewResponse]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/text-to-voice/design",
            method="POST",
            params={
                "output_format": output_format,
            },
            json={
                "voice_description": voice_description,
                "model_id": model_id,
                "text": text,
                "auto_generate_text": auto_generate_text,
                "loudness": loudness,
                "seed": seed,
                "guidance_scale": guidance_scale,
                "stream_previews": stream_previews,
                "quality": quality,
                "reference_audio_base64": reference_audio_base_64,
                "prompt_strength": prompt_strength,
            },
            headers={
                "content-type": "application/json",
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    VoiceDesignPreviewResponse,
                    construct_type(
                        type_=VoiceDesignPreviewResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)
