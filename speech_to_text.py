import logging
import speech_recognition as sr
from groq import Groq
from dotenv import load_dotenv

load_dotenv()

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def record_audio(file_path, timeout=20, phrase_time_limit=None):
    """
    Function to record audio from the microphone and save it as an MP3 file.

    Args:
    file_path (str): Path to save the recorded audio file.
    timeout (int): Maximum time to wait for a phrase to start (in seconds).
    phrase_time_lfimit (int): Maximum time for the phrase to be recorded (in seconds).
    """
    recognizer = sr.Recognizer()
    
    try:
        with sr.Microphone() as source:
            logging.info("Adjusting for ambient noise...")
            recognizer.adjust_for_ambient_noise(source, duration=1)
            logging.info("Start speaking now...")
            
            # Record the audio
            audio_data = recognizer.listen(source, timeout=timeout, phrase_time_limit=phrase_time_limit)
            logging.info("Recording complete.")
            
            # Convert the recorded audio to an MP3 file
            wav_data = audio_data.get_wav_data()
            with open(file_path, 'wb') as f:
                f.write(wav_data)
            
            logging.info(f"Audio saved to {file_path}")

    except Exception as e:
        logging.error(f"An error occurred: {e}")

def transcribe_with_groq(audio_filepath):
    client = Groq()
    stt_model = "whisper-large-v3"

    # Use context manager to ensure file is properly closed
    with open(audio_filepath, "rb") as audio_file:
        transcription = client.audio.transcriptions.create(
            model = stt_model,
            file = audio_file,
            language = "en"
        )

    return transcription.text

if __name__ == "__main__":
    file_path = "speech_to_text.wav"
    record_audio(file_path)
    print(transcribe_with_groq(file_path))