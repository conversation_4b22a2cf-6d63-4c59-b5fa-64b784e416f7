# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .conv_ai_webhooks import ConvAiWebhooks
from .conversation_initiation_client_data_webhook import ConversationInitiationClientDataWebhook


class AgentWorkspaceOverridesOutput(UncheckedBaseModel):
    conversation_initiation_client_data_webhook: typing.Optional[ConversationInitiationClientDataWebhook] = (
        pydantic.Field(default=None)
    )
    """
    The webhook to send conversation initiation client data to
    """

    webhooks: typing.Optional[ConvAiWebhooks] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
