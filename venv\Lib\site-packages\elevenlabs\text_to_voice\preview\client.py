# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ...core.client_wrapper import <PERSON>ync<PERSON><PERSON><PERSON>rapper, Sync<PERSON><PERSON><PERSON>rapper
from ...core.request_options import RequestOptions
from .raw_client import AsyncRawPreviewClient, RawPreviewClient


class PreviewClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawPreviewClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawPreviewClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawPreviewClient
        """
        return self._raw_client

    def stream(
        self, generated_voice_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.Iterator[bytes]:
        """
        Stream a voice preview that was created via the /v1/text-to-voice/design endpoint.

        Parameters
        ----------
        generated_voice_id : str
            The generated_voice_id to stream.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration. You can pass in configuration such as `chunk_size`, and more to customize the request and response.

        Returns
        -------
        typing.Iterator[bytes]
            Streaming audio data
        """
        with self._raw_client.stream(generated_voice_id, request_options=request_options) as r:
            yield from r.data


class AsyncPreviewClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawPreviewClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawPreviewClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawPreviewClient
        """
        return self._raw_client

    async def stream(
        self, generated_voice_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> typing.AsyncIterator[bytes]:
        """
        Stream a voice preview that was created via the /v1/text-to-voice/design endpoint.

        Parameters
        ----------
        generated_voice_id : str
            The generated_voice_id to stream.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration. You can pass in configuration such as `chunk_size`, and more to customize the request and response.

        Returns
        -------
        typing.AsyncIterator[bytes]
            Streaming audio data
        """
        async with self._raw_client.stream(generated_voice_id, request_options=request_options) as r:
            async for _chunk in r.data:
                yield _chunk
