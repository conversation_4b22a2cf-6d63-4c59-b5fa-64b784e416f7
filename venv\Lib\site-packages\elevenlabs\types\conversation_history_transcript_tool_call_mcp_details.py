# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel


class ConversationHistoryTranscriptToolCallMcpDetails(UncheckedBaseModel):
    mcp_server_id: str
    mcp_server_name: str
    integration_type: str
    parameters: typing.Optional[typing.Dict[str, str]] = None
    approval_policy: str
    requires_approval: typing.Optional[bool] = None
    mcp_tool_name: typing.Optional[str] = None
    mcp_tool_description: typing.Optional[str] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
