# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .dubbing_media_reference import DubbingMediaReference
from .render import Render
from .speaker_segment import SpeakerSegment
from .speaker_track import SpeakerTrack


class DubbingResource(UncheckedBaseModel):
    id: str
    version: int
    source_language: str
    target_languages: typing.List[str]
    input: DubbingMediaReference
    background: DubbingMediaReference
    foreground: DubbingMediaReference
    speaker_tracks: typing.Dict[str, SpeakerTrack]
    speaker_segments: typing.Dict[str, SpeakerSegment]
    renders: typing.Dict[str, Render]

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
