# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
import typing_extensions
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.serialization import FieldMetadata
from ..core.unchecked_base_model import UncheckedBaseModel


class Alignment(UncheckedBaseModel):
    """
    Alignment information for the generated audio given the input text sequence.
    """

    char_start_times_ms: typing_extensions.Annotated[
        typing.Optional[typing.List[int]], FieldMetadata(alias="charStartTimesMs")
    ] = pydantic.Field(default=None)
    """
    A list of starting times (in milliseconds) for each character in the text as it
    corresponds to the audio. For instance, the character 'H' starts at time 0 ms in the audio.
    Note these times are relative to the returned chunk from the model, and not the
    full audio response.
    """

    chars_durations_ms: typing_extensions.Annotated[
        typing.Optional[typing.List[int]], FieldMetadata(alias="charsDurationsMs")
    ] = pydantic.Field(default=None)
    """
    A list of durations (in milliseconds) for each character in the text as it
    corresponds to the audio. For instance, the character '<PERSON>' lasts for 3 ms in the audio.
    Note these times are relative to the returned chunk from the model, and not the
    full audio response.
    """

    chars: typing.Optional[typing.List[str]] = pydantic.Field(default=None)
    """
    A list of characters in the text sequence. For instance, the first character is 'H'.
    Note that this list may contain spaces, punctuation, and other special characters.
    The length of this list should be the same as the lengths of `charStartTimesMs` and `charsDurationsMs`.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
