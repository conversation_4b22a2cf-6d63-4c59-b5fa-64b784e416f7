# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations

import typing

import pydantic
import typing_extensions
from ..core.pydantic_utilities import IS_PYDANTIC_V2, update_forward_refs
from ..core.unchecked_base_model import UncheckedBaseModel, UnionMetadata
from .dynamic_variables_config import DynamicVariablesConfig
from .integration_type import IntegrationType
from .mcp_approval_policy import McpApprovalPolicy
from .system_tool_config_input_params import SystemToolConfigInputParams
from .webhook_tool_api_schema_config_input import WebhookToolApiSchemaConfigInput


class ToolRequestModelToolConfig_Client(UncheckedBaseModel):
    """
    Configuration for the tool
    """

    type: typing.Literal["client"] = "client"
    name: str
    description: str
    response_timeout_secs: typing.Optional[int] = None
    parameters: typing.Optional["ObjectJsonSchemaPropertyInput"] = None
    expects_response: typing.Optional[bool] = None
    dynamic_variables: typing.Optional[DynamicVariablesConfig] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class ToolRequestModelToolConfig_Mcp(UncheckedBaseModel):
    """
    Configuration for the tool
    """

    type: typing.Literal["mcp"] = "mcp"
    name: str
    description: str
    response_timeout_secs: typing.Optional[int] = None
    integration_type: IntegrationType
    parameters: typing.Optional["ObjectJsonSchemaPropertyInput"] = None
    approval_policy: typing.Optional[McpApprovalPolicy] = None
    mcp_tool_name: str
    mcp_tool_description: str
    mcp_server_id: str
    mcp_server_name: str
    mcp_input_schema: typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class ToolRequestModelToolConfig_System(UncheckedBaseModel):
    """
    Configuration for the tool
    """

    type: typing.Literal["system"] = "system"
    name: str
    description: str
    response_timeout_secs: typing.Optional[int] = None
    params: SystemToolConfigInputParams

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class ToolRequestModelToolConfig_Webhook(UncheckedBaseModel):
    """
    Configuration for the tool
    """

    type: typing.Literal["webhook"] = "webhook"
    name: str
    description: str
    response_timeout_secs: typing.Optional[int] = None
    api_schema: WebhookToolApiSchemaConfigInput
    dynamic_variables: typing.Optional[DynamicVariablesConfig] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


from .array_json_schema_property_input import ArrayJsonSchemaPropertyInput  # noqa: E402, F401, I001
from .object_json_schema_property_input import ObjectJsonSchemaPropertyInput  # noqa: E402, F401, I001

ToolRequestModelToolConfig = typing_extensions.Annotated[
    typing.Union[
        ToolRequestModelToolConfig_Client,
        ToolRequestModelToolConfig_Mcp,
        ToolRequestModelToolConfig_System,
        ToolRequestModelToolConfig_Webhook,
    ],
    UnionMetadata(discriminant="type"),
]
update_forward_refs(ToolRequestModelToolConfig_Client)
update_forward_refs(ToolRequestModelToolConfig_Mcp)
update_forward_refs(ToolRequestModelToolConfig_Webhook)
