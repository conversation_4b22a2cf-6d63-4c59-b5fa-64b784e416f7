# This file was auto-generated by <PERSON>rn from our API Definition.

import typing

from ..core.client_wrapper import Async<PERSON><PERSON><PERSON>rapper, SyncClientWrapper
from ..core.request_options import RequestOptions
from .raw_client import AsyncRawTextToSoundEffectsClient, RawTextToSoundEffectsClient
from .types.text_to_sound_effects_convert_request_output_format import TextToSoundEffectsConvertRequestOutputFormat

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class TextToSoundEffectsClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawTextToSoundEffectsClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawTextToSoundEffectsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawTextToSoundEffectsClient
        """
        return self._raw_client

    def convert(
        self,
        *,
        text: str,
        output_format: typing.Optional[TextToSoundEffectsConvertRequestOutputFormat] = None,
        duration_seconds: typing.Optional[float] = OMIT,
        prompt_influence: typing.Optional[float] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Iterator[bytes]:
        """
        Turn text into sound effects for your videos, voice-overs or video games using the most advanced sound effects model in the world.

        Parameters
        ----------
        text : str
            The text that will get converted into a sound effect.

        output_format : typing.Optional[TextToSoundEffectsConvertRequestOutputFormat]
            Output format of the generated audio. Formatted as codec_sample_rate_bitrate. So an mp3 with 22.05kHz sample rate at 32kbs is represented as mp3_22050_32. MP3 with 192kbps bitrate requires you to be subscribed to Creator tier or above. PCM with 44.1kHz sample rate requires you to be subscribed to Pro tier or above. Note that the μ-law format (sometimes written mu-law, often approximated as u-law) is commonly used for Twilio audio inputs.

        duration_seconds : typing.Optional[float]
            The duration of the sound which will be generated in seconds. Must be at least 0.5 and at most 22. If set to None we will guess the optimal duration using the prompt. Defaults to None.

        prompt_influence : typing.Optional[float]
            A higher prompt influence makes your generation follow the prompt more closely while also making generations less variable. Must be a value between 0 and 1. Defaults to 0.3.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration. You can pass in configuration such as `chunk_size`, and more to customize the request and response.

        Returns
        -------
        typing.Iterator[bytes]
            The generated sound effect as an MP3 file

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.text_to_sound_effects.convert(
            text="Spacious braam suitable for high-impact movie trailer moments",
        )
        """
        with self._raw_client.convert(
            text=text,
            output_format=output_format,
            duration_seconds=duration_seconds,
            prompt_influence=prompt_influence,
            request_options=request_options,
        ) as r:
            yield from r.data


class AsyncTextToSoundEffectsClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawTextToSoundEffectsClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawTextToSoundEffectsClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawTextToSoundEffectsClient
        """
        return self._raw_client

    async def convert(
        self,
        *,
        text: str,
        output_format: typing.Optional[TextToSoundEffectsConvertRequestOutputFormat] = None,
        duration_seconds: typing.Optional[float] = OMIT,
        prompt_influence: typing.Optional[float] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.AsyncIterator[bytes]:
        """
        Turn text into sound effects for your videos, voice-overs or video games using the most advanced sound effects model in the world.

        Parameters
        ----------
        text : str
            The text that will get converted into a sound effect.

        output_format : typing.Optional[TextToSoundEffectsConvertRequestOutputFormat]
            Output format of the generated audio. Formatted as codec_sample_rate_bitrate. So an mp3 with 22.05kHz sample rate at 32kbs is represented as mp3_22050_32. MP3 with 192kbps bitrate requires you to be subscribed to Creator tier or above. PCM with 44.1kHz sample rate requires you to be subscribed to Pro tier or above. Note that the μ-law format (sometimes written mu-law, often approximated as u-law) is commonly used for Twilio audio inputs.

        duration_seconds : typing.Optional[float]
            The duration of the sound which will be generated in seconds. Must be at least 0.5 and at most 22. If set to None we will guess the optimal duration using the prompt. Defaults to None.

        prompt_influence : typing.Optional[float]
            A higher prompt influence makes your generation follow the prompt more closely while also making generations less variable. Must be a value between 0 and 1. Defaults to 0.3.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration. You can pass in configuration such as `chunk_size`, and more to customize the request and response.

        Returns
        -------
        typing.AsyncIterator[bytes]
            The generated sound effect as an MP3 file

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.text_to_sound_effects.convert(
                text="Spacious braam suitable for high-impact movie trailer moments",
            )


        asyncio.run(main())
        """
        async with self._raw_client.convert(
            text=text,
            output_format=output_format,
            duration_seconds=duration_seconds,
            prompt_influence=prompt_influence,
            request_options=request_options,
        ) as r:
            async for _chunk in r.data:
                yield _chunk
