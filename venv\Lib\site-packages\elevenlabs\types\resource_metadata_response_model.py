# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .share_option_response_model import ShareOptionResponseModel
from .workspace_resource_type import WorkspaceResourceType


class ResourceMetadataResponseModel(UncheckedBaseModel):
    resource_id: str = pydantic.Field()
    """
    The ID of the resource.
    """

    resource_type: WorkspaceResourceType = pydantic.Field()
    """
    The type of the resource.
    """

    creator_user_id: typing.Optional[str] = pydantic.Field(default=None)
    """
    The ID of the user who created the resource.
    """

    role_to_group_ids: typing.Dict[str, typing.List[str]] = pydantic.Field()
    """
    A mapping of roles to group IDs. When the resource is shared with a user, the group id is the user's id.
    """

    share_options: typing.List[ShareOptionResponseModel] = pydantic.Field()
    """
    List of options for sharing the resource further in the workspace. These are users who don't have access to the resource yet.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
