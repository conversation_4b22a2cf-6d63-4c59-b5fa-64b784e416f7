# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel


class PrivacyConfig(UncheckedBaseModel):
    record_voice: typing.Optional[bool] = pydantic.Field(default=None)
    """
    Whether to record the conversation
    """

    retention_days: typing.Optional[int] = pydantic.Field(default=None)
    """
    The number of days to retain the conversation. -1 indicates there is no retention limit
    """

    delete_transcript_and_pii: typing.Optional[bool] = pydantic.Field(default=None)
    """
    Whether to delete the transcript and PII
    """

    delete_audio: typing.Optional[bool] = pydantic.Field(default=None)
    """
    Whether to delete the audio
    """

    apply_to_existing_conversations: typing.Optional[bool] = pydantic.Field(default=None)
    """
    Whether to apply the privacy settings to existing conversations
    """

    zero_retention_mode: typing.Optional[bool] = pydantic.Field(default=None)
    """
    Whether to enable zero retention mode - no PII data is stored
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
