# This file was auto-generated by <PERSON>rn from our API Definition.

from __future__ import annotations

import typing

from .literal_json_schema_property import LiteralJsonSchemaProperty

if typing.TYPE_CHECKING:
    from .array_json_schema_property_output import ArrayJsonSchemaPropertyOutput
    from .object_json_schema_property_output import ObjectJsonSchemaPropertyOutput
ArrayJsonSchemaPropertyOutputItems = typing.Union[
    LiteralJsonSchemaProperty, "ObjectJsonSchemaPropertyOutput", "ArrayJsonSchemaPropertyOutput"
]
