# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .conv_ai_webhooks import ConvAiWebhooks
from .conversation_initiation_client_data_webhook import ConversationInitiationClientDataWebhook


class GetConvAiSettingsResponseModel(UncheckedBaseModel):
    conversation_initiation_client_data_webhook: typing.Optional[ConversationInitiationClientDataWebhook] = None
    webhooks: typing.Optional[ConvAiWebhooks] = None
    can_use_mcp_servers: typing.Optional[bool] = pydantic.Field(default=None)
    """
    Whether the workspace can use MCP servers
    """

    rag_retention_period_days: typing.Optional[int] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
