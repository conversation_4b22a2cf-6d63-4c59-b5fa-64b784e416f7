# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel


class AudioNativeEditContentResponseModel(UncheckedBaseModel):
    project_id: str = pydantic.Field()
    """
    The ID of the project.
    """

    converting: bool = pydantic.Field()
    """
    Whether the project is currently being converted.
    """

    publishing: bool = pydantic.Field()
    """
    Whether the project is currently being published.
    """

    html_snippet: str = pydantic.Field()
    """
    The HTML snippet to embed the Audio Native player.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
