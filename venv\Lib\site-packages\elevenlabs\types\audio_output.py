# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
import typing_extensions
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.serialization import FieldMetadata
from ..core.unchecked_base_model import UncheckedBaseModel
from .alignment import Alignment
from .normalized_alignment import NormalizedAlignment


class AudioOutput(UncheckedBaseModel):
    audio: str = pydantic.Field()
    """
    A generated partial audio chunk, encoded using the selected output_format, by default this
    is MP3 encoded as a base64 string.
    """

    normalized_alignment: typing_extensions.Annotated[
        typing.Optional[NormalizedAlignment], FieldMetadata(alias="normalizedAlignment")
    ] = None
    alignment: typing.Optional[Alignment] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
