# This file was auto-generated by Fern from our API Definition.

from __future__ import annotations

import typing

from .literal_json_schema_property import LiteralJsonSchemaProperty

if typing.TYPE_CHECKING:
    from .array_json_schema_property_output import ArrayJsonSchemaPropertyOutput
    from .object_json_schema_property_output import ObjectJsonSchemaPropertyOutput
ObjectJsonSchemaPropertyOutputPropertiesValue = typing.Union[
    LiteralJsonSchemaProperty, "ObjectJsonSchemaPropertyOutput", "ArrayJsonSchemaPropertyOutput"
]
