# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from .... import core
from ....core.client_wrapper import <PERSON>ync<PERSON><PERSON><PERSON>rapper, SyncClientWrapper
from ....core.request_options import RequestOptions
from ....types.add_voice_response_model import AddVoiceResponseModel
from ....types.delete_voice_sample_response_model import DeleteVoiceSampleResponseModel
from ....types.voice_sample import VoiceSample
from .audio.client import AsyncAudio<PERSON>lient, AudioClient
from .raw_client import AsyncRawSamplesClient, RawSamplesClient
from .speakers.client import AsyncSpeakersClient, SpeakersClient
from .waveform.client import AsyncWaveformClient, WaveformClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class SamplesClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawSamplesClient(client_wrapper=client_wrapper)
        self.audio = AudioClient(client_wrapper=client_wrapper)

        self.waveform = WaveformClient(client_wrapper=client_wrapper)

        self.speakers = SpeakersClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawSamplesClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawSamplesClient
        """
        return self._raw_client

    def create(
        self,
        voice_id: str,
        *,
        files: typing.List[core.File],
        remove_background_noise: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.List[VoiceSample]:
        """
        Add audio samples to a PVC voice

        Parameters
        ----------
        voice_id : str
            Voice ID to be used, you can use https://api.elevenlabs.io/v1/voices to list all the available voices.

        files : typing.List[core.File]
            See core.File for more documentation

        remove_background_noise : typing.Optional[bool]
            If set will remove background noise for voice samples using our audio isolation model. If the samples do not include background noise, it can make the quality worse.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[VoiceSample]
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.voices.pvc.samples.create(
            voice_id="21m00Tcm4TlvDq8ikWAM",
        )
        """
        _response = self._raw_client.create(
            voice_id, files=files, remove_background_noise=remove_background_noise, request_options=request_options
        )
        return _response.data

    def update(
        self,
        voice_id: str,
        sample_id: str,
        *,
        remove_background_noise: typing.Optional[bool] = OMIT,
        selected_speaker_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        trim_start_time: typing.Optional[int] = OMIT,
        trim_end_time: typing.Optional[int] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddVoiceResponseModel:
        """
        Update a PVC voice sample - apply noise removal, or select speaker.

        Parameters
        ----------
        voice_id : str
            Voice ID to be used, you can use https://api.elevenlabs.io/v1/voices to list all the available voices.

        sample_id : str
            Sample ID to be used

        remove_background_noise : typing.Optional[bool]
            If set will remove background noise for voice samples using our audio isolation model. If the samples do not include background noise, it can make the quality worse.

        selected_speaker_ids : typing.Optional[typing.Sequence[str]]
            Speaker IDs to be used for PVC training. Make sure you send all the speaker IDs you want to use for PVC training in one request because the last request will override the previous ones.

        trim_start_time : typing.Optional[int]
            The start time of the audio to be used for PVC training. Time should be in milliseconds

        trim_end_time : typing.Optional[int]
            The end time of the audio to be used for PVC training. Time should be in milliseconds

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddVoiceResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.voices.pvc.samples.update(
            voice_id="21m00Tcm4TlvDq8ikWAM",
            sample_id="VW7YKqPnjY4h39yTbx2L",
        )
        """
        _response = self._raw_client.update(
            voice_id,
            sample_id,
            remove_background_noise=remove_background_noise,
            selected_speaker_ids=selected_speaker_ids,
            trim_start_time=trim_start_time,
            trim_end_time=trim_end_time,
            request_options=request_options,
        )
        return _response.data

    def delete(
        self, voice_id: str, sample_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> DeleteVoiceSampleResponseModel:
        """
        Delete a sample from a PVC voice.

        Parameters
        ----------
        voice_id : str
            Voice ID to be used, you can use https://api.elevenlabs.io/v1/voices to list all the available voices.

        sample_id : str
            Sample ID to be used

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DeleteVoiceSampleResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.voices.pvc.samples.delete(
            voice_id="21m00Tcm4TlvDq8ikWAM",
            sample_id="VW7YKqPnjY4h39yTbx2L",
        )
        """
        _response = self._raw_client.delete(voice_id, sample_id, request_options=request_options)
        return _response.data


class AsyncSamplesClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawSamplesClient(client_wrapper=client_wrapper)
        self.audio = AsyncAudioClient(client_wrapper=client_wrapper)

        self.waveform = AsyncWaveformClient(client_wrapper=client_wrapper)

        self.speakers = AsyncSpeakersClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawSamplesClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawSamplesClient
        """
        return self._raw_client

    async def create(
        self,
        voice_id: str,
        *,
        files: typing.List[core.File],
        remove_background_noise: typing.Optional[bool] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.List[VoiceSample]:
        """
        Add audio samples to a PVC voice

        Parameters
        ----------
        voice_id : str
            Voice ID to be used, you can use https://api.elevenlabs.io/v1/voices to list all the available voices.

        files : typing.List[core.File]
            See core.File for more documentation

        remove_background_noise : typing.Optional[bool]
            If set will remove background noise for voice samples using our audio isolation model. If the samples do not include background noise, it can make the quality worse.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        typing.List[VoiceSample]
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.voices.pvc.samples.create(
                voice_id="21m00Tcm4TlvDq8ikWAM",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.create(
            voice_id, files=files, remove_background_noise=remove_background_noise, request_options=request_options
        )
        return _response.data

    async def update(
        self,
        voice_id: str,
        sample_id: str,
        *,
        remove_background_noise: typing.Optional[bool] = OMIT,
        selected_speaker_ids: typing.Optional[typing.Sequence[str]] = OMIT,
        trim_start_time: typing.Optional[int] = OMIT,
        trim_end_time: typing.Optional[int] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AddVoiceResponseModel:
        """
        Update a PVC voice sample - apply noise removal, or select speaker.

        Parameters
        ----------
        voice_id : str
            Voice ID to be used, you can use https://api.elevenlabs.io/v1/voices to list all the available voices.

        sample_id : str
            Sample ID to be used

        remove_background_noise : typing.Optional[bool]
            If set will remove background noise for voice samples using our audio isolation model. If the samples do not include background noise, it can make the quality worse.

        selected_speaker_ids : typing.Optional[typing.Sequence[str]]
            Speaker IDs to be used for PVC training. Make sure you send all the speaker IDs you want to use for PVC training in one request because the last request will override the previous ones.

        trim_start_time : typing.Optional[int]
            The start time of the audio to be used for PVC training. Time should be in milliseconds

        trim_end_time : typing.Optional[int]
            The end time of the audio to be used for PVC training. Time should be in milliseconds

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddVoiceResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.voices.pvc.samples.update(
                voice_id="21m00Tcm4TlvDq8ikWAM",
                sample_id="VW7YKqPnjY4h39yTbx2L",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.update(
            voice_id,
            sample_id,
            remove_background_noise=remove_background_noise,
            selected_speaker_ids=selected_speaker_ids,
            trim_start_time=trim_start_time,
            trim_end_time=trim_end_time,
            request_options=request_options,
        )
        return _response.data

    async def delete(
        self, voice_id: str, sample_id: str, *, request_options: typing.Optional[RequestOptions] = None
    ) -> DeleteVoiceSampleResponseModel:
        """
        Delete a sample from a PVC voice.

        Parameters
        ----------
        voice_id : str
            Voice ID to be used, you can use https://api.elevenlabs.io/v1/voices to list all the available voices.

        sample_id : str
            Sample ID to be used

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DeleteVoiceSampleResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.voices.pvc.samples.delete(
                voice_id="21m00Tcm4TlvDq8ikWAM",
                sample_id="VW7YKqPnjY4h39yTbx2L",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.delete(voice_id, sample_id, request_options=request_options)
        return _response.data
