# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel


class ChapterSnapshotResponse(UncheckedBaseModel):
    chapter_snapshot_id: str = pydantic.Field()
    """
    The ID of the chapter snapshot.
    """

    project_id: str = pydantic.Field()
    """
    The ID of the project.
    """

    chapter_id: str = pydantic.Field()
    """
    The ID of the chapter.
    """

    created_at_unix: int = pydantic.Field()
    """
    The creation date of the chapter snapshot.
    """

    name: str = pydantic.Field()
    """
    The name of the chapter snapshot.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
