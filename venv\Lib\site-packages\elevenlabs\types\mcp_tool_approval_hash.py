# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .mcp_tool_approval_policy import McpToolApprovalPolicy


class McpToolApprovalHash(UncheckedBaseModel):
    """
    Model for storing tool approval hashes for per-tool approval.
    """

    tool_name: str = pydantic.Field()
    """
    The name of the MCP tool
    """

    tool_hash: str = pydantic.Field()
    """
    SHA256 hash of the tool's parameters and description
    """

    approval_policy: typing.Optional[McpToolApprovalPolicy] = pydantic.Field(default=None)
    """
    The approval policy for this tool
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
