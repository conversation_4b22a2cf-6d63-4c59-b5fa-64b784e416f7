# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .conv_ai_stored_secret_dependencies_agents_item import ConvAiStoredSecretDependenciesAgentsItem
from .conv_ai_stored_secret_dependencies_tools_item import ConvAiStoredSecretDependenciesToolsItem
from .dependent_phone_number_identifier import DependentPhoneNumberIdentifier
from .secret_dependency_type import SecretDependencyType


class ConvAiStoredSecretDependencies(UncheckedBaseModel):
    tools: typing.List[ConvAiStoredSecretDependenciesToolsItem]
    agents: typing.List[ConvAiStoredSecretDependenciesAgentsItem]
    others: typing.List[SecretDependencyType]
    phone_numbers: typing.Optional[typing.List[DependentPhoneNumberIdentifier]] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
