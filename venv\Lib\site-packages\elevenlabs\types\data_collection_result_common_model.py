# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .literal_json_schema_property import LiteralJsonSchemaProperty


class DataCollectionResultCommonModel(UncheckedBaseModel):
    data_collection_id: str
    value: typing.Optional[typing.Optional[typing.Any]] = None
    json_schema: typing.Optional[LiteralJsonSchemaProperty] = None
    rationale: str

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
