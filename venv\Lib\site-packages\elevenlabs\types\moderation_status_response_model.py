# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .moderation_status_response_model_safety_status import ModerationStatusResponseModelSafetyStatus
from .moderation_status_response_model_warning_status import ModerationStatusResponseModelWarningStatus


class ModerationStatusResponseModel(UncheckedBaseModel):
    is_in_probation: bool = pydantic.Field()
    """
    Whether the user is in probation.
    """

    enterprise_check_nogo_voice: bool = pydantic.Field()
    """
    Whether the user's enterprise check nogo voice is enabled.
    """

    enterprise_check_block_nogo_voice: bool = pydantic.Field()
    """
    Whether the user's enterprise check block nogo voice is enabled.
    """

    never_live_moderate: bool = pydantic.Field()
    """
    Whether the user's never live moderate is enabled.
    """

    nogo_voice_similar_voice_upload_count: int = pydantic.Field()
    """
    The number of similar voice uploads that have been blocked.
    """

    enterprise_background_moderation_enabled: bool = pydantic.Field()
    """
    Whether the user's enterprise background moderation is enabled.
    """

    safety_status: typing.Optional[ModerationStatusResponseModelSafetyStatus] = pydantic.Field(default=None)
    """
    The safety status of the user.
    """

    warning_status: typing.Optional[ModerationStatusResponseModelWarningStatus] = pydantic.Field(default=None)
    """
    The warning status of the user.
    """

    on_watchlist: bool = pydantic.Field()
    """
    Whether the user is on the watchlist.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
