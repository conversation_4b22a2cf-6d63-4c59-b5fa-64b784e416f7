# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations

import typing

import pydantic
import typing_extensions
from ..core.pydantic_utilities import IS_PYDANTIC_V2, update_forward_refs
from ..core.unchecked_base_model import UncheckedBaseModel, UnionMetadata
from .dynamic_variables_config import DynamicVariablesConfig
from .integration_type import IntegrationType
from .mcp_approval_policy import McpApprovalPolicy
from .system_tool_config_output_params import SystemToolConfigOutputParams
from .webhook_tool_api_schema_config_output import WebhookToolApiSchemaConfigOutput


class PromptAgentApiModelOutputToolsItem_Client(UncheckedBaseModel):
    """
    The type of tool
    """

    type: typing.Literal["client"] = "client"
    name: str
    description: str
    response_timeout_secs: typing.Optional[int] = None
    parameters: typing.Optional["ObjectJsonSchemaPropertyOutput"] = None
    expects_response: typing.Optional[bool] = None
    dynamic_variables: typing.Optional[DynamicVariablesConfig] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class PromptAgentApiModelOutputToolsItem_Mcp(UncheckedBaseModel):
    """
    The type of tool
    """

    type: typing.Literal["mcp"] = "mcp"
    name: str
    description: str
    response_timeout_secs: typing.Optional[int] = None
    integration_type: IntegrationType
    parameters: typing.Optional["ObjectJsonSchemaPropertyOutput"] = None
    approval_policy: typing.Optional[McpApprovalPolicy] = None
    mcp_tool_name: str
    mcp_tool_description: str
    mcp_server_id: str
    mcp_server_name: str
    mcp_input_schema: typing.Optional[typing.Dict[str, typing.Optional[typing.Any]]] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class PromptAgentApiModelOutputToolsItem_System(UncheckedBaseModel):
    """
    The type of tool
    """

    type: typing.Literal["system"] = "system"
    name: str
    description: str
    response_timeout_secs: typing.Optional[int] = None
    params: SystemToolConfigOutputParams

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class PromptAgentApiModelOutputToolsItem_Webhook(UncheckedBaseModel):
    """
    The type of tool
    """

    type: typing.Literal["webhook"] = "webhook"
    name: str
    description: str
    response_timeout_secs: typing.Optional[int] = None
    api_schema: WebhookToolApiSchemaConfigOutput
    dynamic_variables: typing.Optional[DynamicVariablesConfig] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


from .array_json_schema_property_output import ArrayJsonSchemaPropertyOutput  # noqa: E402, F401, I001
from .object_json_schema_property_output import ObjectJsonSchemaPropertyOutput  # noqa: E402, F401, I001

PromptAgentApiModelOutputToolsItem = typing_extensions.Annotated[
    typing.Union[
        PromptAgentApiModelOutputToolsItem_Client,
        PromptAgentApiModelOutputToolsItem_Mcp,
        PromptAgentApiModelOutputToolsItem_System,
        PromptAgentApiModelOutputToolsItem_Webhook,
    ],
    UnionMetadata(discriminant="type"),
]
update_forward_refs(PromptAgentApiModelOutputToolsItem_Client)
update_forward_refs(PromptAgentApiModelOutputToolsItem_Mcp)
update_forward_refs(PromptAgentApiModelOutputToolsItem_Webhook)
