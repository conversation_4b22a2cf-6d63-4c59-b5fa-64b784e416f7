# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .system_tool_config_output_params import SystemToolConfigOutputParams


class SystemToolConfigOutput(UncheckedBaseModel):
    """
    A system tool is a tool that is used to call a system method in the server
    """

    name: str
    description: str
    response_timeout_secs: typing.Optional[int] = pydantic.Field(default=None)
    """
    The maximum time in seconds to wait for the tool call to complete.
    """

    params: SystemToolConfigOutputParams

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
