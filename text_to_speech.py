import os
import elevenlabs
from elevenlabs.client import Eleven<PERSON>abs
import subprocess
import platform
from dotenv import load_dotenv

load_dotenv()

ELEVENLABS_API_KEY = os.environ.get("ELEVENLABS_API_KEY")

def text_to_speech_with_elevenlabs(input_text, output_filepath):
    client = ElevenLabs(api_key=ELEVENLABS_API_KEY)
    audio = client.text_to_speech.convert(
        text = input_text,
        voice_id = "ZF6FPAbjXT4488VcRRnw", #"kdmDKE6EkgrWrrykO9Qt",  "JBFqnCBsd6RMkjVDRZzb" 
        model_id = "eleven_multilingual_v2",
        output_format = "mp3_22050_32" 
    )
    elevenlabs.save(audio, output_filepath)
    os_name = platform.system()
    try:
        if os_name == "Darwin":  # macOS
            subprocess.run(['afplay', output_filepath])
        elif os_name == "Windows":  # Windows
            # # Use a non-blocking approach to avoid file locking
            # subprocess.Popen(['powershell', '-c', f'(New-Object Media.SoundPlayer "{os.path.abspath(output_filepath)}").PlaySync()'],
            #                creationflags=subprocess.CREATE_NO_WINDOW)
            subprocess.run(['powershell', '-c', f'Start-Process "{output_filepath}"'])
        elif os_name == "Linux":  # Linux
            subprocess.run(['aplay', output_filepath])  # Alternative: use 'mpg123' or 'ffplay'
        else:
            raise OSError("Unsupported operating system")
    except Exception as e:
        print(f"An error occurred while trying to play the audio: {e}")


from gtts import gTTS

def text_to_speech_with_gtts(input_text, output_filepath):
    language = "en"

    audioobj = gTTS(
        text = input_text,
        lang = language,
        slow = False
    )
    audioobj.save(output_filepath)
    os_name = platform.system()
    try:
        if os_name == "Darwin":  # macOS
            subprocess.run(['afplay', output_filepath])
        elif os_name == "Windows":  # Windows
            # # Use a non-blocking approach to avoid file locking
            # subprocess.Popen(['powershell', '-c', f'(New-Object Media.SoundPlayer "{os.path.abspath(output_filepath)}").PlaySync()'],
            #                creationflags=subprocess.CREATE_NO_WINDOW)
            subprocess.run(['powershell', '-c', f'Start-Process "{output_filepath}"'])
        elif os_name == "Linux":  # Linux
            subprocess.run(['aplay', output_filepath])  # Alternative: use 'mpg123' or 'ffplay'
        else:
            raise OSError("Unsupported operating system")
    except Exception as e:
        print(f"An error occurred while trying to play the audio: {e}")


if __name__ == "__main__":
    input_text = "Hi, I am doing fine, how are you? This is a test to see how the ElevenLabs AI voice Sounds."
    output_filepath = "test_text_to_speech.mp3"
    text_to_speech_with_elevenlabs(input_text, output_filepath)
    #text_to_speech_with_gtts(input_text, output_filepath)