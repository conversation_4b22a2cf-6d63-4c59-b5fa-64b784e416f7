# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .verification_attempt_response import VerificationAttemptResponse


class VoiceVerificationResponse(UncheckedBaseModel):
    requires_verification: bool = pydantic.Field()
    """
    Whether the voice requires verification.
    """

    is_verified: bool = pydantic.Field()
    """
    Whether the voice has been verified.
    """

    verification_failures: typing.List[str] = pydantic.Field()
    """
    List of verification failures.
    """

    verification_attempts_count: int = pydantic.Field()
    """
    The number of verification attempts.
    """

    language: typing.Optional[str] = pydantic.Field(default=None)
    """
    The language of the voice.
    """

    verification_attempts: typing.Optional[typing.List[VerificationAttemptResponse]] = pydantic.Field(default=None)
    """
    Number of times a verification was attempted.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
