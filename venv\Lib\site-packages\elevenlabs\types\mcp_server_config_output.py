# This file was auto-generated by <PERSON>rn from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .mcp_approval_policy import McpApprovalPolicy
from .mcp_server_config_output_request_headers_value import McpServerConfigOutputRequestHeadersValue
from .mcp_server_config_output_secret_token import McpServerConfigOutputSecretToken
from .mcp_server_config_output_url import McpServerConfigOutputUrl
from .mcp_server_transport import McpServerTransport
from .mcp_tool_approval_hash import McpToolApprovalHash


class McpServerConfigOutput(UncheckedBaseModel):
    approval_policy: typing.Optional[McpApprovalPolicy] = None
    tool_approval_hashes: typing.Optional[typing.List[McpToolApprovalHash]] = pydantic.Field(default=None)
    """
    List of tool approval hashes for per-tool approval when approval_policy is REQUIRE_APPROVAL_PER_TOOL
    """

    transport: typing.Optional[McpServerTransport] = pydantic.Field(default=None)
    """
    The transport type used to connect to the MCP server
    """

    url: McpServerConfigOutputUrl = pydantic.Field()
    """
    The URL of the MCP server, if this contains a secret please store as a workspace secret, otherwise store as a plain string. Must use https
    """

    secret_token: typing.Optional[McpServerConfigOutputSecretToken] = pydantic.Field(default=None)
    """
    The secret token (Authorization header) stored as a workspace secret or in-place secret
    """

    request_headers: typing.Optional[typing.Dict[str, McpServerConfigOutputRequestHeadersValue]] = pydantic.Field(
        default=None
    )
    """
    The headers included in the request
    """

    name: str
    description: typing.Optional[str] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
