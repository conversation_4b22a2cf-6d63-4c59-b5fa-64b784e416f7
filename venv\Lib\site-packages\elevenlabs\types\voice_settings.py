# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel


class VoiceSettings(UncheckedBaseModel):
    stability: typing.Optional[float] = pydantic.Field(default=None)
    """
    Determines how stable the voice is and the randomness between each generation. Lower values introduce broader emotional range for the voice. Higher values can result in a monotonous voice with limited emotion.
    """

    use_speaker_boost: typing.Optional[bool] = pydantic.Field(default=None)
    """
    This setting boosts the similarity to the original speaker. Using this setting requires a slightly higher computational load, which in turn increases latency.
    """

    similarity_boost: typing.Optional[float] = pydantic.Field(default=None)
    """
    Determines how closely the AI should adhere to the original voice when attempting to replicate it.
    """

    style: typing.Optional[float] = pydantic.Field(default=None)
    """
    Determines the style exaggeration of the voice. This setting attempts to amplify the style of the original speaker. It does consume additional computational resources and might increase latency if set to anything other than 0.
    """

    speed: typing.Optional[float] = pydantic.Field(default=None)
    """
    Adjusts the speed of the voice. A value of 1.0 is the default speed, while values less than 1.0 slow down the speech, and values greater than 1.0 speed it up.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
