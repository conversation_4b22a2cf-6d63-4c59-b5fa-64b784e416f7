# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel


class SkipTurnToolConfig(UncheckedBaseModel):
    """
    Allows the agent to explicitly skip its turn.

    This tool should be invoked by the LLM when the user indicates they would like
    to think or take a short pause before continuing the conversation—e.g. when
    they say: "Give me a second", "Let me think", or "One moment please".  After
    calling this tool, the assistant should not speak until the user speaks
    again, or another normal turn-taking condition is met.  The tool itself has
    no parameters and performs no side-effects other than informing the backend
    that the current turn generation is complete.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
