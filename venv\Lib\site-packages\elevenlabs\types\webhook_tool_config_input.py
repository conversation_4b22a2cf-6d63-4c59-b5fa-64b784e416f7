# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2, update_forward_refs
from ..core.unchecked_base_model import UncheckedBaseModel
from .dynamic_variables_config import DynamicVariablesConfig
from .webhook_tool_api_schema_config_input import WebhookToolApiSchemaConfigInput


class WebhookToolConfigInput(UncheckedBaseModel):
    """
    A webhook tool is a tool that calls an external webhook from our server
    """

    name: str
    description: str
    response_timeout_secs: typing.Optional[int] = pydantic.Field(default=None)
    """
    The maximum time in seconds to wait for the tool call to complete. Must be between 5 and 120 seconds (inclusive).
    """

    api_schema: WebhookToolApiSchemaConfigInput = pydantic.Field()
    """
    The schema for the outgoing webhoook, including parameters and URL specification
    """

    dynamic_variables: typing.Optional[DynamicVariablesConfig] = pydantic.Field(default=None)
    """
    Configuration for dynamic variables
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


from .array_json_schema_property_input import ArrayJsonSchemaPropertyInput  # noqa: E402, F401, I001
from .object_json_schema_property_input import ObjectJsonSchemaPropertyInput  # noqa: E402, F401, I001

update_forward_refs(WebhookToolConfigInput)
