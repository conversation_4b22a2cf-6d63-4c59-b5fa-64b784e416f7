# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .speech_history_item_response import SpeechHistoryItemResponse


class GetSpeechHistoryResponse(UncheckedBaseModel):
    history: typing.List[SpeechHistoryItemResponse] = pydantic.Field()
    """
    A list of speech history items.
    """

    last_history_item_id: typing.Optional[str] = pydantic.Field(default=None)
    """
    The ID of the last history item.
    """

    has_more: bool = pydantic.Field()
    """
    Whether there are more history items to fetch.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
