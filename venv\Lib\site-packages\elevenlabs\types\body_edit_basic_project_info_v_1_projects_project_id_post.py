# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel


class BodyEditBasicProjectInfoV1ProjectsProjectIdPost(UncheckedBaseModel):
    name: str = pydantic.Field()
    """
    The name of the Studio project, used for identification only.
    """

    default_title_voice_id: str = pydantic.Field()
    """
    The voice_id that corresponds to the default voice used for new titles.
    """

    default_paragraph_voice_id: str = pydantic.Field()
    """
    The voice_id that corresponds to the default voice used for new paragraphs.
    """

    title: typing.Optional[str] = pydantic.Field(default=None)
    """
    An optional name of the author of the Studio project, this will be added as metadata to the mp3 file on Studio project or chapter download.
    """

    author: typing.Optional[str] = pydantic.Field(default=None)
    """
    An optional name of the author of the Studio project, this will be added as metadata to the mp3 file on Studio project or chapter download.
    """

    isbn_number: typing.Optional[str] = pydantic.Field(default=None)
    """
    An optional ISBN number of the Studio project you want to create, this will be added as metadata to the mp3 file on Studio project or chapter download.
    """

    volume_normalization: typing.Optional[bool] = pydantic.Field(default=None)
    """
    When the Studio project is downloaded, should the returned audio have postprocessing in order to make it compliant with audiobook normalized volume requirements
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
