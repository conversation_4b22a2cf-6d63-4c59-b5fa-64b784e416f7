# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

import pydantic
import typing_extensions
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.serialization import FieldMetadata
from ..core.unchecked_base_model import UncheckedBaseModel
from .alignment import Alignment
from .normalized_alignment import NormalizedAlignment


class WebsocketTtsServerMessageMulti(UncheckedBaseModel):
    """
    Message sent from the server to the client for the multi-context TTS WebSocket.
    """

    audio: typing.Optional[str] = pydantic.Field(default=None)
    """
    A generated partial audio chunk, encoded using the selected output_format (e.g., MP3 as a base64 string).
    """

    is_final: typing.Optional[bool] = pydantic.Field(default=None)
    """
    If true, indicates that this is the final message for the specified contextId. This is sent when a context is closed. `audio` will be null or empty.
    """

    normalized_alignment: typing_extensions.Annotated[
        typing.Optional[NormalizedAlignment], FieldMetadata(alias="normalizedAlignment")
    ] = None
    alignment: typing.Optional[Alignment] = None
    context_id: typing_extensions.Annotated[typing.Optional[str], FieldMetadata(alias="contextId")] = pydantic.Field(
        default=None
    )
    """
    The context identifier to which this message pertains.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
