# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .history_alignment_response_model import HistoryAlignmentResponseModel


class HistoryAlignmentsResponseModel(UncheckedBaseModel):
    alignment: HistoryAlignmentResponseModel = pydantic.Field()
    """
    The alignment of the text.
    """

    normalized_alignment: HistoryAlignmentResponseModel = pydantic.Field()
    """
    The normalized alignment of the text.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
