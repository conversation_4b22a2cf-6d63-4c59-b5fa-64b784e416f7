# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .webhook_auth_method_type import WebhookAuthMethodType
from .workspace_webhook_usage_response_model import WorkspaceWebhookUsageResponseModel


class WorkspaceWebhookResponseModel(UncheckedBaseModel):
    name: str = pydantic.Field()
    """
    The display name for this webhook.
    """

    webhook_id: str = pydantic.Field()
    """
    The unique ID for this webhook.
    """

    webhook_url: str = pydantic.Field()
    """
    The HTTPS callback URL that is called when this webhook is triggered in the platform.
    """

    is_disabled: bool = pydantic.Field()
    """
    Whether the webhook has been manually disabled by a user.
    """

    is_auto_disabled: bool = pydantic.Field()
    """
    Whether the webhook has been automatically disabled due to repeated consecutive failures over a long period of time.
    """

    created_at_unix: int = pydantic.Field()
    """
    Original creation time of the webhook.
    """

    auth_type: WebhookAuthMethodType = pydantic.Field()
    """
    The authentication mode used to secure the webhook.
    """

    usage: typing.Optional[typing.List[WorkspaceWebhookUsageResponseModel]] = pydantic.Field(default=None)
    """
    The list of products that are currently configured to trigger this webhook.
    """

    most_recent_failure_error_code: typing.Optional[int] = pydantic.Field(default=None)
    """
    The most recent error code returned from the callback URL.
    """

    most_recent_failure_timestamp: typing.Optional[int] = pydantic.Field(default=None)
    """
    The most recent time the webhook failed, failures are any non-200 codes returned by the callback URL.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
