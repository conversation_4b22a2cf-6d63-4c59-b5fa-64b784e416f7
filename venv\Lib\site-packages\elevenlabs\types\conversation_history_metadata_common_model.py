# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .authorization_method import AuthorizationMethod
from .conversation_charging_common_model import ConversationChargingCommonModel
from .conversation_deletion_settings import ConversationDeletionSettings
from .conversation_history_batch_call_model import Conversation<PERSON>istoryBatchCallModel
from .conversation_history_eleven_assistant_common_model import ConversationHistoryElevenAssistantCommonModel
from .conversation_history_error_common_model import ConversationHistoryErrorCommonModel
from .conversation_history_feedback_common_model import ConversationHistoryFeedbackCommonModel
from .conversation_history_metadata_common_model_phone_call import ConversationHistoryMetadataCommonModelPhoneCall
from .conversation_history_rag_usage_common_model import ConversationHistoryRagUsageCommonModel
from .conversation_initiation_source import ConversationInitiationSource
from .features_usage_common_model import FeaturesUsageCommonModel


class ConversationHistoryMetadataCommonModel(UncheckedBaseModel):
    start_time_unix_secs: int
    accepted_time_unix_secs: typing.Optional[int] = None
    call_duration_secs: int
    cost: typing.Optional[int] = None
    deletion_settings: typing.Optional[ConversationDeletionSettings] = None
    feedback: typing.Optional[ConversationHistoryFeedbackCommonModel] = None
    authorization_method: typing.Optional[AuthorizationMethod] = None
    charging: typing.Optional[ConversationChargingCommonModel] = None
    phone_call: typing.Optional[ConversationHistoryMetadataCommonModelPhoneCall] = None
    batch_call: typing.Optional[ConversationHistoryBatchCallModel] = None
    termination_reason: typing.Optional[str] = None
    error: typing.Optional[ConversationHistoryErrorCommonModel] = None
    main_language: typing.Optional[str] = None
    rag_usage: typing.Optional[ConversationHistoryRagUsageCommonModel] = None
    text_only: typing.Optional[bool] = None
    features_usage: typing.Optional[FeaturesUsageCommonModel] = None
    eleven_assistant: typing.Optional[ConversationHistoryElevenAssistantCommonModel] = None
    initiator_id: typing.Optional[str] = None
    conversation_initiation_source: typing.Optional[ConversationInitiationSource] = None
    conversation_initiation_source_version: typing.Optional[str] = None
    timezone: typing.Optional[str] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
