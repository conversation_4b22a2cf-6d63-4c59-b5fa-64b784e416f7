# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .widget_text_contents import WidgetTextContents


class WidgetLanguagePresetResponse(UncheckedBaseModel):
    first_message: typing.Optional[str] = None
    text_contents: typing.Optional[WidgetTextContents] = pydantic.Field(default=None)
    """
    The text contents for the selected language
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
