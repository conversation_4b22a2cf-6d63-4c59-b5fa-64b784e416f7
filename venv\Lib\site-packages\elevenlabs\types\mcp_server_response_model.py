# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .mcp_server_config_output import McpServerConfigOutput
from .mcp_server_metadata_response_model import McpServerMetadataResponseModel
from .mcp_server_response_model_dependent_agents_item import McpServerResponseModelDependentAgentsItem
from .resource_access_info import ResourceAccessInfo


class McpServerResponseModel(UncheckedBaseModel):
    """
    Response model representing an MCP Server configuration.
    """

    id: str
    config: McpServerConfigOutput
    access_info: typing.Optional[ResourceAccessInfo] = pydantic.Field(default=None)
    """
    The access information of the MCP Server
    """

    dependent_agents: typing.Optional[typing.List[McpServerResponseModelDependentAgentsItem]] = pydantic.Field(
        default=None
    )
    """
    List of agents that depend on this MCP Server.
    """

    metadata: McpServerMetadataResponseModel = pydantic.Field()
    """
    The metadata of the MCP Server
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
