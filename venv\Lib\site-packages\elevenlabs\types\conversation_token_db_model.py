# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .conversation_token_purpose import ConversationTokenPurpose


class ConversationTokenDbModel(UncheckedBaseModel):
    agent_id: str = pydantic.Field()
    """
    The ID of the agent
    """

    conversation_token: str = pydantic.Field()
    """
    The token for the agent
    """

    expiration_time_unix_secs: typing.Optional[int] = pydantic.Field(default=None)
    """
    The expiration time of the token in unix seconds
    """

    conversation_id: typing.Optional[str] = pydantic.Field(default=None)
    """
    The ID of the conversation
    """

    purpose: typing.Optional[ConversationTokenPurpose] = pydantic.Field(default=None)
    """
    The purpose of the token
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
