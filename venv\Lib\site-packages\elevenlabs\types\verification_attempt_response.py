# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .recording_response import RecordingResponse


class VerificationAttemptResponse(UncheckedBaseModel):
    text: str = pydantic.Field()
    """
    The text of the verification attempt.
    """

    date_unix: int = pydantic.Field()
    """
    The date of the verification attempt in Unix time.
    """

    accepted: bool = pydantic.Field()
    """
    Whether the verification attempt was accepted.
    """

    similarity: float = pydantic.Field()
    """
    The similarity of the verification attempt.
    """

    levenshtein_distance: float = pydantic.Field()
    """
    The Levenshtein distance of the verification attempt.
    """

    recording: typing.Optional[RecordingResponse] = pydantic.Field(default=None)
    """
    The recording of the verification attempt.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
