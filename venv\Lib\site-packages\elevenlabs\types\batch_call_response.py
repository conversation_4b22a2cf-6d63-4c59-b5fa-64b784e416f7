# This file was auto-generated by <PERSON>rn from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .batch_call_status import BatchCallStatus
from .telephony_provider import TelephonyProvider


class BatchCallResponse(UncheckedBaseModel):
    id: str
    phone_number_id: str
    phone_provider: typing.Optional[TelephonyProvider] = None
    name: str
    agent_id: str
    created_at_unix: int
    scheduled_time_unix: int
    total_calls_dispatched: int
    total_calls_scheduled: int
    last_updated_at_unix: int
    status: BatchCallStatus
    agent_name: str

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
