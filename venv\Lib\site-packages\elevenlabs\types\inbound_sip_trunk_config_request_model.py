# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .sip_media_encryption_enum import SipMediaEncryptionEnum
from .sip_trunk_credentials_request_model import SipTrunkCredentialsRequestModel


class InboundSipTrunkConfigRequestModel(UncheckedBaseModel):
    allowed_addresses: typing.Optional[typing.List[str]] = pydantic.Field(default=None)
    """
    List of IP addresses that are allowed to use the trunk. Each item in the list can be an individual IP address or a Classless Inter-Domain Routing notation representing a CIDR block.
    """

    allowed_numbers: typing.Optional[typing.List[str]] = pydantic.Field(default=None)
    """
    List of phone numbers that are allowed to use the trunk.
    """

    media_encryption: typing.Optional[SipMediaEncryptionEnum] = pydantic.Field(default=None)
    """
    Whether or not to encrypt media (data layer).
    """

    credentials: typing.Optional[SipTrunkCredentialsRequestModel] = pydantic.Field(default=None)
    """
    Optional digest authentication credentials (username/password).
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
