# This file was auto-generated by <PERSON>rn from our API Definition.

from __future__ import annotations

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2, update_forward_refs
from ..core.unchecked_base_model import UncheckedBaseModel


class ArrayJsonSchemaPropertyOutput(UncheckedBaseModel):
    type: typing.Optional[typing.Literal["array"]] = None
    description: typing.Optional[str] = None
    items: "ArrayJsonSchemaPropertyOutputItems"

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


from .object_json_schema_property_output import ObjectJsonSchemaPropertyOutput  # noqa: E402, F401, I001
from .array_json_schema_property_output_items import ArrayJsonSchemaPropertyOutputItems  # noqa: E402, F401, I001

update_forward_refs(ArrayJsonSchemaPropertyOutput)
