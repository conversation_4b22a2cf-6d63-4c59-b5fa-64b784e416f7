# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing
from json.decoder import JSONDecodeError

from ... import core
from ...core.api_error import ApiError
from ...core.client_wrapper import Async<PERSON>lient<PERSON>rapper, SyncClientWrapper
from ...core.http_response import AsyncHttpResponse, HttpResponse
from ...core.request_options import RequestOptions
from ...core.unchecked_base_model import construct_type
from ...errors.unprocessable_entity_error import UnprocessableEntityError
from ...types.add_voice_ivc_response_model import AddVoiceIvcResponseModel
from ...types.http_validation_error import HttpValidationError

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class RawIvcClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    def create(
        self,
        *,
        name: str,
        files: typing.List[core.File],
        remove_background_noise: typing.Optional[bool] = OMIT,
        description: typing.Optional[str] = OMIT,
        labels: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> HttpResponse[AddVoiceIvcResponseModel]:
        """
        Create a voice clone and add it to your Voices

        Parameters
        ----------
        name : str
            The name that identifies this voice. This will be displayed in the dropdown of the website.

        files : typing.List[core.File]
            See core.File for more documentation

        remove_background_noise : typing.Optional[bool]
            If set will remove background noise for voice samples using our audio isolation model. If the samples do not include background noise, it can make the quality worse.

        description : typing.Optional[str]
            A description of the voice.

        labels : typing.Optional[str]
            Serialized labels dictionary for the voice.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        HttpResponse[AddVoiceIvcResponseModel]
            Successful Response
        """
        _response = self._client_wrapper.httpx_client.request(
            "v1/voices/add",
            method="POST",
            data={
                "name": name,
                "remove_background_noise": remove_background_noise,
                "description": description,
                "labels": labels,
            },
            files={
                "files": files,
            },
            request_options=request_options,
            omit=OMIT,
            force_multipart=True,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    AddVoiceIvcResponseModel,
                    construct_type(
                        type_=AddVoiceIvcResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return HttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)


class AsyncRawIvcClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def create(
        self,
        *,
        name: str,
        files: typing.List[core.File],
        remove_background_noise: typing.Optional[bool] = OMIT,
        description: typing.Optional[str] = OMIT,
        labels: typing.Optional[str] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AsyncHttpResponse[AddVoiceIvcResponseModel]:
        """
        Create a voice clone and add it to your Voices

        Parameters
        ----------
        name : str
            The name that identifies this voice. This will be displayed in the dropdown of the website.

        files : typing.List[core.File]
            See core.File for more documentation

        remove_background_noise : typing.Optional[bool]
            If set will remove background noise for voice samples using our audio isolation model. If the samples do not include background noise, it can make the quality worse.

        description : typing.Optional[str]
            A description of the voice.

        labels : typing.Optional[str]
            Serialized labels dictionary for the voice.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncHttpResponse[AddVoiceIvcResponseModel]
            Successful Response
        """
        _response = await self._client_wrapper.httpx_client.request(
            "v1/voices/add",
            method="POST",
            data={
                "name": name,
                "remove_background_noise": remove_background_noise,
                "description": description,
                "labels": labels,
            },
            files={
                "files": files,
            },
            request_options=request_options,
            omit=OMIT,
            force_multipart=True,
        )
        try:
            if 200 <= _response.status_code < 300:
                _data = typing.cast(
                    AddVoiceIvcResponseModel,
                    construct_type(
                        type_=AddVoiceIvcResponseModel,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                return AsyncHttpResponse(response=_response, data=_data)
            if _response.status_code == 422:
                raise UnprocessableEntityError(
                    headers=dict(_response.headers),
                    body=typing.cast(
                        HttpValidationError,
                        construct_type(
                            type_=HttpValidationError,  # type: ignore
                            object_=_response.json(),
                        ),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response.text)
        raise ApiError(status_code=_response.status_code, headers=dict(_response.headers), body=_response_json)
