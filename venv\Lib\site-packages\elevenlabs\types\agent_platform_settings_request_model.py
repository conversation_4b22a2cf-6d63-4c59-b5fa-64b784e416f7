# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .agent_call_limits import AgentCallLimits
from .agent_testing_settings import AgentTestingSettings
from .agent_workspace_overrides_input import AgentWorkspaceOverridesInput
from .auth_settings import AuthSettings
from .conversation_initiation_client_data_config_input import ConversationInitiationClientDataConfigInput
from .evaluation_settings import EvaluationSettings
from .literal_json_schema_property import LiteralJsonSchemaProperty
from .privacy_config import PrivacyConfig
from .widget_config import WidgetConfig


class AgentPlatformSettingsRequestModel(UncheckedBaseModel):
    auth: typing.Optional[AuthSettings] = pydantic.Field(default=None)
    """
    Settings for authentication
    """

    evaluation: typing.Optional[EvaluationSettings] = pydantic.Field(default=None)
    """
    Settings for evaluation
    """

    widget: typing.Optional[WidgetConfig] = pydantic.Field(default=None)
    """
    Configuration for the widget
    """

    data_collection: typing.Optional[typing.Dict[str, LiteralJsonSchemaProperty]] = pydantic.Field(default=None)
    """
    Data collection settings
    """

    overrides: typing.Optional[ConversationInitiationClientDataConfigInput] = pydantic.Field(default=None)
    """
    Additional overrides for the agent during conversation initiation
    """

    call_limits: typing.Optional[AgentCallLimits] = pydantic.Field(default=None)
    """
    Call limits for the agent
    """

    privacy: typing.Optional[PrivacyConfig] = pydantic.Field(default=None)
    """
    Privacy settings for the agent
    """

    workspace_overrides: typing.Optional[AgentWorkspaceOverridesInput] = pydantic.Field(default=None)
    """
    Workspace overrides for the agent
    """

    testing: typing.Optional[AgentTestingSettings] = pydantic.Field(default=None)
    """
    Testing configuration for the agent
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
