# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .tool import Tool


class ListMcpToolsResponseModel(UncheckedBaseModel):
    """
    Response model for testing tools available on an MCP server.
    """

    success: bool = pydantic.Field()
    """
    Indicates if the operation was successful.
    """

    tools: typing.List[Tool] = pydantic.Field()
    """
    A list of tools available on the MCP server.
    """

    error_message: typing.Optional[str] = pydantic.Field(default=None)
    """
    Error message if the operation was not successful.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
