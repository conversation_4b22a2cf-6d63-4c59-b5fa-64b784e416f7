# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .resource_access_info import ResourceAccessInfo


class AgentSummaryResponseModel(UncheckedBaseModel):
    agent_id: str = pydantic.Field()
    """
    The ID of the agent
    """

    name: str = pydantic.Field()
    """
    The name of the agent
    """

    tags: typing.List[str] = pydantic.Field()
    """
    Agent tags used to categorize the agent
    """

    created_at_unix_secs: int = pydantic.Field()
    """
    The creation time of the agent in unix seconds
    """

    access_info: ResourceAccessInfo = pydantic.Field()
    """
    The access information of the agent
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
