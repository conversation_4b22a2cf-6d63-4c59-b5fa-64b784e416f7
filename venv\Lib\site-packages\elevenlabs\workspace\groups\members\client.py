# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

from ....core.client_wrapper import AsyncC<PERSON><PERSON>rap<PERSON>, SyncClientWrapper
from ....core.request_options import RequestOptions
from ....types.add_workspace_group_member_response_model import AddWorkspaceGroupMemberResponseModel
from ....types.delete_workspace_group_member_response_model import DeleteWorkspaceGroupMemberResponseModel
from .raw_client import AsyncRawMembersClient, RawMembersClient

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class MembersClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._raw_client = RawMembersClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> RawMembersClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        RawMembersClient
        """
        return self._raw_client

    def remove(
        self, group_id: str, *, email: str, request_options: typing.Optional[RequestOptions] = None
    ) -> DeleteWorkspaceGroupMemberResponseModel:
        """
        Removes a member from the specified group. This endpoint may only be called by workspace administrators.

        Parameters
        ----------
        group_id : str
            The ID of the target group.

        email : str
            The email of the target workspace member.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DeleteWorkspaceGroupMemberResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.workspace.groups.members.remove(
            group_id="group_id",
            email="email",
        )
        """
        _response = self._raw_client.remove(group_id, email=email, request_options=request_options)
        return _response.data

    def add(
        self, group_id: str, *, email: str, request_options: typing.Optional[RequestOptions] = None
    ) -> AddWorkspaceGroupMemberResponseModel:
        """
        Adds a member of your workspace to the specified group. This endpoint may only be called by workspace administrators.

        Parameters
        ----------
        group_id : str
            The ID of the target group.

        email : str
            The email of the target workspace member.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddWorkspaceGroupMemberResponseModel
            Successful Response

        Examples
        --------
        from elevenlabs import ElevenLabs

        client = ElevenLabs(
            api_key="YOUR_API_KEY",
        )
        client.workspace.groups.members.add(
            group_id="group_id",
            email="email",
        )
        """
        _response = self._raw_client.add(group_id, email=email, request_options=request_options)
        return _response.data


class AsyncMembersClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._raw_client = AsyncRawMembersClient(client_wrapper=client_wrapper)

    @property
    def with_raw_response(self) -> AsyncRawMembersClient:
        """
        Retrieves a raw implementation of this client that returns raw responses.

        Returns
        -------
        AsyncRawMembersClient
        """
        return self._raw_client

    async def remove(
        self, group_id: str, *, email: str, request_options: typing.Optional[RequestOptions] = None
    ) -> DeleteWorkspaceGroupMemberResponseModel:
        """
        Removes a member from the specified group. This endpoint may only be called by workspace administrators.

        Parameters
        ----------
        group_id : str
            The ID of the target group.

        email : str
            The email of the target workspace member.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        DeleteWorkspaceGroupMemberResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.workspace.groups.members.remove(
                group_id="group_id",
                email="email",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.remove(group_id, email=email, request_options=request_options)
        return _response.data

    async def add(
        self, group_id: str, *, email: str, request_options: typing.Optional[RequestOptions] = None
    ) -> AddWorkspaceGroupMemberResponseModel:
        """
        Adds a member of your workspace to the specified group. This endpoint may only be called by workspace administrators.

        Parameters
        ----------
        group_id : str
            The ID of the target group.

        email : str
            The email of the target workspace member.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AddWorkspaceGroupMemberResponseModel
            Successful Response

        Examples
        --------
        import asyncio

        from elevenlabs import AsyncElevenLabs

        client = AsyncElevenLabs(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.workspace.groups.members.add(
                group_id="group_id",
                email="email",
            )


        asyncio.run(main())
        """
        _response = await self._raw_client.add(group_id, email=email, request_options=request_options)
        return _response.data
