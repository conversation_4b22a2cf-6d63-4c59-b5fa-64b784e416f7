from speech_to_text import record_audio, transcribe_with_groq
from ai_agent import ask_agent
from text_to_speech import text_to_speech_with_elevenlabs, text_to_speech_with_gtts
from dotenv import load_dotenv

load_dotenv()

audio_filepath = "audio_question.wav"

def process_audio_and_chat():
    chat_history = []
    while True:
        try:
            record_audio(file_path=audio_filepath)
            user_input = transcribe_with_groq(audio_filepath)

            if "goodbye" in user_input.lower():
                break

            response = ask_agent(user_input)

            voice_of_doctor = text_to_speech_with_elevenlabs(input_text=response, output_filepath="audio_response.mp3")

            chat_history.append([user_input, response])

            yield chat_history

        except Exception as e:
            print(f"Error in continuous recording: {e}")
            break

if __name__ == "__main__":
    for chat_history in process_audio_and_chat():
        print(chat_history)