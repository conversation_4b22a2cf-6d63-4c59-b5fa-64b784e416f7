from speech_to_text import record_audio, transcribe_with_groq
from ai_agent import ask_agent
from text_to_speech import text_to_speech_with_elevenlabs, text_to_speech_with_gtts
from dotenv import load_dotenv
import os
import time
import uuid

load_dotenv()

audio_filepath = "audio_question.wav"

def process_audio_and_chat():
    chat_history = []
    iteration = 0
    while True:
        try:
            # Clean up previous audio files to prevent file locking issues
            if iteration > 0:
                time.sleep(1)  # Give time for any media players to release the file
                cleanup_audio_files()

            record_audio(file_path=audio_filepath)
            user_input = transcribe_with_groq(audio_filepath)

            if "goodbye" in user_input.lower():
                print("Goodbye! Ending conversation.")
                break

            response = ask_agent(user_input)

            # Use unique filename for each iteration to avoid file locking
            unique_response_file = f"audio_response_{uuid.uuid4().hex[:8]}.mp3"
            text_to_speech_with_elevenlabs(input_text=response, output_filepath=unique_response_file)

            chat_history.append([user_input, response])
            iteration += 1

            yield chat_history

        except Exception as e:
            print(f"Error in continuous recording: {e}")
            break

    # Final cleanup
    cleanup_audio_files()

def cleanup_audio_files():
    """Clean up audio files to prevent accumulation and file locking issues"""
    try:
        # Remove old audio response files
        for filename in os.listdir('.'):
            if filename.startswith('audio_response_') and filename.endswith('.mp3'):
                try:
                    os.remove(filename)
                except (OSError, PermissionError):
                    # File might still be in use, skip it
                    pass
    except Exception as e:
        # Don't let cleanup errors break the main loop
        pass

if __name__ == "__main__":
    process_audio_and_chat()