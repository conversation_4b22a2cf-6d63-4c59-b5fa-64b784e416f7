# This file was auto-generated by Fe<PERSON> from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .pydantic_pronunciation_dictionary_version_locator import PydanticPronunciationDictionaryVersionLocator
from .supported_voice import SupportedVoice
from .tts_conversational_model import TtsConversationalModel
from .tts_optimize_streaming_latency import TtsOptimizeStreamingLatency
from .tts_output_format import TtsOutputFormat


class TtsConversationalConfigOutput(UncheckedBaseModel):
    model_id: typing.Optional[TtsConversationalModel] = pydantic.Field(default=None)
    """
    The model to use for TTS
    """

    voice_id: typing.Optional[str] = pydantic.Field(default=None)
    """
    The voice ID to use for TTS
    """

    supported_voices: typing.Optional[typing.List[SupportedVoice]] = pydantic.Field(default=None)
    """
    Additional supported voices for the agent
    """

    agent_output_audio_format: typing.Optional[TtsOutputFormat] = pydantic.Field(default=None)
    """
    The audio format to use for TTS
    """

    optimize_streaming_latency: typing.Optional[TtsOptimizeStreamingLatency] = pydantic.Field(default=None)
    """
    The optimization for streaming latency
    """

    stability: typing.Optional[float] = pydantic.Field(default=None)
    """
    The stability of generated speech
    """

    speed: typing.Optional[float] = pydantic.Field(default=None)
    """
    The speed of generated speech
    """

    similarity_boost: typing.Optional[float] = pydantic.Field(default=None)
    """
    The similarity boost for generated speech
    """

    pronunciation_dictionary_locators: typing.Optional[typing.List[PydanticPronunciationDictionaryVersionLocator]] = (
        pydantic.Field(default=None)
    )
    """
    The pronunciation dictionary locators
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
