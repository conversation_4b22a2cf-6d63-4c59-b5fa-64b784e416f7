# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .voice_preview_response_model import VoicePreviewResponseModel


class VoiceDesignPreviewResponse(UncheckedBaseModel):
    previews: typing.List[VoicePreviewResponseModel] = pydantic.Field()
    """
    The previews of the generated voices.
    """

    text: str = pydantic.Field()
    """
    The text used to preview the voices.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
