# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .embedding_model_enum import EmbeddingModelEnum
from .rag_chunk_metadata import RagChunkMetadata


class RagRetrievalInfo(UncheckedBaseModel):
    chunks: typing.List[RagChunkMetadata]
    embedding_model: EmbeddingModelEnum
    retrieval_query: str
    rag_latency_secs: float

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
