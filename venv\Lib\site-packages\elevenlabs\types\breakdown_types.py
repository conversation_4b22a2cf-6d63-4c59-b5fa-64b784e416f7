# This file was auto-generated by Fern from our API Definition.

import typing

BreakdownTypes = typing.Union[
    typing.Literal[
        "none",
        "voice",
        "voice_multiplier",
        "user",
        "groups",
        "api_keys",
        "all_api_keys",
        "product_type",
        "model",
        "resource",
        "request_queue",
        "region",
        "subresource_id",
        "reporting_workspace_id",
    ],
    typing.Any,
]
