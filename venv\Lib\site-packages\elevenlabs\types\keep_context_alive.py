# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel


class KeepContextAlive(UncheckedBaseModel):
    """
    Payload to keep a specific context alive by resetting its inactivity timeout. Empty text is ignored but resets the clock.
    """

    text: typing.Literal[""] = pydantic.Field(default="")
    """
    An empty string. This text is ignored by the server but its presence resets the inactivity timeout for the specified context.
    """

    context_id: str = pydantic.Field()
    """
    The identifier of the context to keep alive.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
