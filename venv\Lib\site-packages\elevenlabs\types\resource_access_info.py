# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .resource_access_info_role import ResourceAccessInfoRole


class ResourceAccessInfo(UncheckedBaseModel):
    is_creator: bool = pydantic.Field()
    """
    Whether the user making the request is the creator of the agent
    """

    creator_name: str = pydantic.Field()
    """
    Name of the agent's creator
    """

    creator_email: str = pydantic.Field()
    """
    Email of the agent's creator
    """

    role: ResourceAccessInfoRole = pydantic.Field()
    """
    The role of the user making the request
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
