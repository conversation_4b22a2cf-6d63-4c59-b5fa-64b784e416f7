# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel


class WidgetStyles(UncheckedBaseModel):
    base: typing.Optional[str] = pydantic.Field(default=None)
    """
    The base background color.
    """

    base_hover: typing.Optional[str] = pydantic.Field(default=None)
    """
    The color of the base background when hovered.
    """

    base_active: typing.Optional[str] = pydantic.Field(default=None)
    """
    The color of the base background when active (clicked).
    """

    base_border: typing.Optional[str] = pydantic.Field(default=None)
    """
    The color of the border against the base background.
    """

    base_subtle: typing.Optional[str] = pydantic.Field(default=None)
    """
    The color of subtle text against the base background.
    """

    base_primary: typing.Optional[str] = pydantic.Field(default=None)
    """
    The color of primary text against the base background.
    """

    base_error: typing.Optional[str] = pydantic.Field(default=None)
    """
    The color of error text against the base background.
    """

    accent: typing.Optional[str] = pydantic.Field(default=None)
    """
    The accent background color.
    """

    accent_hover: typing.Optional[str] = pydantic.Field(default=None)
    """
    The color of the accent background when hovered.
    """

    accent_active: typing.Optional[str] = pydantic.Field(default=None)
    """
    The color of the accent background when active (clicked).
    """

    accent_border: typing.Optional[str] = pydantic.Field(default=None)
    """
    The color of the border against the accent background.
    """

    accent_subtle: typing.Optional[str] = pydantic.Field(default=None)
    """
    The color of subtle text against the accent background.
    """

    accent_primary: typing.Optional[str] = pydantic.Field(default=None)
    """
    The color of primary text against the accent background.
    """

    overlay_padding: typing.Optional[float] = pydantic.Field(default=None)
    """
    The padding around the edges of the viewport.
    """

    button_radius: typing.Optional[float] = pydantic.Field(default=None)
    """
    The radius of the buttons.
    """

    input_radius: typing.Optional[float] = pydantic.Field(default=None)
    """
    The radius of the input fields.
    """

    bubble_radius: typing.Optional[float] = pydantic.Field(default=None)
    """
    The radius of the chat bubbles.
    """

    sheet_radius: typing.Optional[float] = pydantic.Field(default=None)
    """
    The default radius of sheets.
    """

    compact_sheet_radius: typing.Optional[float] = pydantic.Field(default=None)
    """
    The radius of the sheet in compact mode.
    """

    dropdown_sheet_radius: typing.Optional[float] = pydantic.Field(default=None)
    """
    The radius of the dropdown sheet.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
