# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .inbound_sip_trunk_config_request_model import InboundSipTrunkConfigRequestModel
from .outbound_sip_trunk_config_request_model import OutboundSipTrunkConfigRequestModel


class CreateSipTrunkPhoneNumberRequest(UncheckedBaseModel):
    phone_number: str = pydantic.Field()
    """
    Phone number
    """

    label: str = pydantic.Field()
    """
    Label for the phone number
    """

    supports_inbound: typing.Optional[bool] = pydantic.Field(default=None)
    """
    Whether this phone number supports inbound calls
    """

    supports_outbound: typing.Optional[bool] = pydantic.Field(default=None)
    """
    Whether this phone number supports outbound calls
    """

    inbound_trunk_config: typing.Optional[InboundSipTrunkConfigRequestModel] = None
    outbound_trunk_config: typing.Optional[OutboundSipTrunkConfigRequestModel] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
