# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .agent_metadata_response_model import AgentMetadataResponseModel
from .agent_platform_settings_response_model import AgentPlatformSettingsResponseModel
from .conversational_config import ConversationalConfig
from .get_agent_response_model_phone_numbers_item import GetAgentResponseModelPhoneNumbersItem
from .resource_access_info import ResourceAccessInfo


class GetAgentResponseModel(UncheckedBaseModel):
    agent_id: str = pydantic.Field()
    """
    The ID of the agent
    """

    name: str = pydantic.Field()
    """
    The name of the agent
    """

    conversation_config: ConversationalConfig = pydantic.Field()
    """
    The conversation configuration of the agent
    """

    metadata: AgentMetadataResponseModel = pydantic.Field()
    """
    The metadata of the agent
    """

    platform_settings: typing.Optional[AgentPlatformSettingsResponseModel] = pydantic.Field(default=None)
    """
    The platform settings of the agent
    """

    phone_numbers: typing.Optional[typing.List[GetAgentResponseModelPhoneNumbersItem]] = pydantic.Field(default=None)
    """
    The phone numbers of the agent
    """

    workflow: typing.Optional[typing.Optional[typing.Any]] = None
    access_info: typing.Optional[ResourceAccessInfo] = pydantic.Field(default=None)
    """
    The access information of the agent for the user
    """

    tags: typing.Optional[typing.List[str]] = pydantic.Field(default=None)
    """
    Agent tags used to categorize the agent
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
