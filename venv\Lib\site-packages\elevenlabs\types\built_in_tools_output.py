# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .system_tool_config_output import SystemToolConfigOutput


class BuiltInToolsOutput(UncheckedBaseModel):
    end_call: typing.Optional[SystemToolConfigOutput] = pydantic.Field(default=None)
    """
    The end call tool
    """

    language_detection: typing.Optional[SystemToolConfigOutput] = pydantic.Field(default=None)
    """
    The language detection tool
    """

    transfer_to_agent: typing.Optional[SystemToolConfigOutput] = pydantic.Field(default=None)
    """
    The transfer to agent tool
    """

    transfer_to_number: typing.Optional[SystemToolConfigOutput] = pydantic.Field(default=None)
    """
    The transfer to number tool
    """

    skip_turn: typing.Optional[SystemToolConfigOutput] = pydantic.Field(default=None)
    """
    The skip turn tool
    """

    play_keypad_touch_tone: typing.Optional[SystemToolConfigOutput] = pydantic.Field(default=None)
    """
    The play DTMF tool
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
