# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .dubbing_media_reference import DubbingMediaReference


class DubbedSegment(UncheckedBaseModel):
    start_time: float
    end_time: float
    text: typing.Optional[str] = None
    audio_stale: bool
    media_ref: typing.Optional[DubbingMediaReference] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
