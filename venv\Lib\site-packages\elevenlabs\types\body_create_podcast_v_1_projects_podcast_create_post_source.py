# This file was auto-generated by Fern from our API Definition.

import typing

from .body_create_podcast_v_1_projects_podcast_create_post_source_item import (
    BodyCreatePodcastV1ProjectsPodcastCreatePostSourceItem,
)
from .podcast_text_source import PodcastTextSource
from .podcast_url_source import PodcastUrlSource

BodyCreatePodcastV1ProjectsPodcastCreatePostSource = typing.Union[
    PodcastTextSource, PodcastUrlSource, typing.List[BodyCreatePodcastV1ProjectsPodcastCreatePostSourceItem]
]
