# This file was auto-generated by <PERSON>rn from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .llm_tokens_category_usage import LlmTokensCategoryUsage


class LlmInputOutputTokensUsage(UncheckedBaseModel):
    input: typing.Optional[LlmTokensCategoryUsage] = None
    input_cache_read: typing.Optional[LlmTokensCategoryUsage] = None
    input_cache_write: typing.Optional[LlmTokensCategoryUsage] = None
    output_total: typing.Optional[LlmTokensCategoryUsage] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
