# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .chapter_content_input_model import ChapterContentInputModel


class BodyEditChapterV1ProjectsProjectIdChaptersChapterIdPatch(UncheckedBaseModel):
    name: typing.Optional[str] = pydantic.Field(default=None)
    """
    The name of the chapter, used for identification only.
    """

    content: typing.Optional[ChapterContentInputModel] = pydantic.Field(default=None)
    """
    The chapter content to use.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
