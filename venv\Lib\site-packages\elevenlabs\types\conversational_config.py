# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .agent_config import AgentConfig
from .asr_conversational_config import AsrConversationalConfig
from .conversation_config import ConversationConfig
from .language_preset_output import LanguagePresetOutput
from .tts_conversational_config_output import TtsConversationalConfigOutput
from .turn_config import TurnConfig


class ConversationalConfig(UncheckedBaseModel):
    asr: typing.Optional[AsrConversationalConfig] = pydantic.Field(default=None)
    """
    Configuration for conversational transcription
    """

    turn: typing.Optional[TurnConfig] = pydantic.Field(default=None)
    """
    Configuration for turn detection
    """

    tts: typing.Optional[TtsConversationalConfigOutput] = pydantic.Field(default=None)
    """
    Configuration for conversational text to speech
    """

    conversation: typing.Optional[ConversationConfig] = pydantic.Field(default=None)
    """
    Configuration for conversational events
    """

    language_presets: typing.Optional[typing.Dict[str, LanguagePresetOutput]] = pydantic.Field(default=None)
    """
    Language presets for conversations
    """

    agent: typing.Optional[AgentConfig] = pydantic.Field(default=None)
    """
    Agent specific configuration
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
