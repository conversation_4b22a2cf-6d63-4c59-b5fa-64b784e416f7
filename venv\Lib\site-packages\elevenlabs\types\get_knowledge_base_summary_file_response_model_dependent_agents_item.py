# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations

import typing

import pydantic
import typing_extensions
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel, UnionMetadata
from .dependent_available_agent_identifier_access_level import DependentAvailableAgentIdentifierAccessLevel


class GetKnowledgeBaseSummaryFileResponseModelDependentAgentsItem_Available(UncheckedBaseModel):
    type: typing.Literal["available"] = "available"
    id: str
    name: str
    created_at_unix_secs: int
    access_level: DependentAvailableAgentIdentifierAccessLevel

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class GetKnowledgeBaseSummaryFileResponseModelDependentAgentsItem_Unknown(UncheckedBaseModel):
    type: typing.Literal["unknown"] = "unknown"

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


GetKnowledgeBaseSummaryFileResponseModelDependentAgentsItem = typing_extensions.Annotated[
    typing.Union[
        GetKnowledgeBaseSummaryFileResponseModelDependentAgentsItem_Available,
        GetKnowledgeBaseSummaryFileResponseModelDependentAgentsItem_Unknown,
    ],
    UnionMetadata(discriminant="type"),
]
