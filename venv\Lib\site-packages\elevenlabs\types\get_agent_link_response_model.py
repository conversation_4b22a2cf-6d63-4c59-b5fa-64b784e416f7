# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .conversation_token_db_model import ConversationTokenDbModel


class GetAgentLinkResponseModel(UncheckedBaseModel):
    agent_id: str = pydantic.Field()
    """
    The ID of the agent
    """

    token: typing.Optional[ConversationTokenDbModel] = pydantic.Field(default=None)
    """
    The token data for the agent
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
