# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .embedding_model_enum import EmbeddingModelEnum
from .rag_document_index_usage import RagDocumentIndexUsage
from .rag_index_status import RagIndexStatus


class RagDocumentIndexResponseModel(UncheckedBaseModel):
    id: str
    model: EmbeddingModelEnum
    status: RagIndexStatus
    progress_percentage: float
    document_model_index_usage: RagDocumentIndexUsage

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
