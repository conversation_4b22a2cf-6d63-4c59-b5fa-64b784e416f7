# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .body_create_podcast_v_1_projects_podcast_create_post_duration_scale import (
    BodyCreatePodcastV1ProjectsPodcastCreatePostDurationScale,
)
from .body_create_podcast_v_1_projects_podcast_create_post_mode import BodyCreatePodcastV1ProjectsPodcastCreatePostMode
from .body_create_podcast_v_1_projects_podcast_create_post_quality_preset import (
    BodyCreatePodcastV1ProjectsPodcastCreatePostQualityPreset,
)
from .body_create_podcast_v_1_projects_podcast_create_post_source import (
    BodyCreatePodcastV1ProjectsPodcastCreatePostSource,
)


class BodyCreatePodcastV1ProjectsPodcastCreatePost(UncheckedBaseModel):
    model_id: str = pydantic.Field()
    """
    The ID of the model to be used for this Studio project, you can query GET /v1/models to list all available models.
    """

    mode: BodyCreatePodcastV1ProjectsPodcastCreatePostMode = pydantic.Field()
    """
    The type of podcast to generate. Can be 'conversation', an interaction between two voices, or 'bulletin', a monologue.
    """

    source: BodyCreatePodcastV1ProjectsPodcastCreatePostSource = pydantic.Field()
    """
    The source content for the Podcast.
    """

    quality_preset: typing.Optional[BodyCreatePodcastV1ProjectsPodcastCreatePostQualityPreset] = pydantic.Field(
        default=None
    )
    """
    Output quality of the generated audio. Must be one of:
    standard - standard output format, 128kbps with 44.1kHz sample rate.
    high - high quality output format, 192kbps with 44.1kHz sample rate and major improvements on our side. Using this setting increases the credit cost by 20%.
    ultra - ultra quality output format, 192kbps with 44.1kHz sample rate and highest improvements on our side. Using this setting increases the credit cost by 50%.
    ultra lossless - ultra quality output format, 705.6kbps with 44.1kHz sample rate and highest improvements on our side in a fully lossless format. Using this setting increases the credit cost by 100%.
    """

    duration_scale: typing.Optional[BodyCreatePodcastV1ProjectsPodcastCreatePostDurationScale] = pydantic.Field(
        default=None
    )
    """
    Duration of the generated podcast. Must be one of:
    short - produces podcasts shorter than 3 minutes.
    default - produces podcasts roughly between 3-7 minutes.
    long - produces podcasts longer than 7 minutes.
    """

    language: typing.Optional[str] = pydantic.Field(default=None)
    """
    An optional language of the Studio project. Two-letter language code (ISO 639-1).
    """

    intro: typing.Optional[str] = pydantic.Field(default=None)
    """
    The intro text that will always be added to the beginning of the podcast.
    """

    outro: typing.Optional[str] = pydantic.Field(default=None)
    """
    The outro text that will always be added to the end of the podcast.
    """

    instructions_prompt: typing.Optional[str] = pydantic.Field(default=None)
    """
    Additional instructions prompt for the podcast generation used to adjust the podcast's style and tone.
    """

    highlights: typing.Optional[typing.List[str]] = pydantic.Field(default=None)
    """
    A brief summary or highlights of the Studio project's content, providing key points or themes. This should be between 10 and 70 characters.
    """

    callback_url: typing.Optional[str] = pydantic.Field(default=None)
    """
    
        A url that will be called by our service when the Studio project is converted. Request will contain a json blob containing the status of the conversion
        Messages:
        1. When project was converted successfully:
        {
          type: "project_conversion_status",
          event_timestamp: 1234567890,
          data: {
            request_id: "1234567890",
            project_id: "21m00Tcm4TlvDq8ikWAM",
            conversion_status: "success",
            project_snapshot_id: "22m00Tcm4TlvDq8ikMAT",
            error_details: None,
          }
        }
        2. When project conversion failed:
        {
          type: "project_conversion_status",
          event_timestamp: 1234567890,
          data: {
            request_id: "1234567890",
            project_id: "21m00Tcm4TlvDq8ikWAM",
            conversion_status: "error",
            project_snapshot_id: None,
            error_details: "Error details if conversion failed"
          }
        }
    
        3. When chapter was converted successfully:
        {
          type: "chapter_conversion_status",
          event_timestamp: 1234567890,
          data: {
            request_id: "1234567890",
            project_id: "21m00Tcm4TlvDq8ikWAM",
            chapter_id: "22m00Tcm4TlvDq8ikMAT",
            conversion_status: "success",
            chapter_snapshot_id: "23m00Tcm4TlvDq8ikMAV",
            error_details: None,
          }
        }
        4. When chapter conversion failed:
        {
          type: "chapter_conversion_status",
          event_timestamp: 1234567890,
          data: {
            request_id: "1234567890",
            project_id: "21m00Tcm4TlvDq8ikWAM",
            chapter_id: "22m00Tcm4TlvDq8ikMAT",
            conversion_status: "error",
            chapter_snapshot_id: None,
            error_details: "Error details if conversion failed"
          }
        }
        
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
