# This file was auto-generated by Fern from our API Definition.

import typing

import pydantic
from ..core.pydantic_utilities import IS_PYDANTIC_V2
from ..core.unchecked_base_model import UncheckedBaseModel
from .workspace_webhook_response_model import WorkspaceWebhookResponseModel


class WorkspaceWebhookListResponseModel(UncheckedBaseModel):
    webhooks: typing.List[WorkspaceWebhookResponseModel] = pydantic.Field()
    """
    List of webhooks currently configured for the workspace
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
